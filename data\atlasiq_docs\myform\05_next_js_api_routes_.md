# Chapter 5: Next.js API Routes

Welcome back to the Myform tutorial! In the past few chapters ([Chapter 1: User Authentication & Management](01_user_authentication___management_.md), [Chapter 2: Form Structure & Management](02_form_structure___management_.md), [Chapter 3: Form Responses & Analytics](03_form_responses___analytics_.md), and [Chapter 4: AI Integration](04_ai_integration_.md)), we've seen how different parts of Myform talk to a "backend" or "server" to get data, save things, or trigger actions like talking to the Atlas API or the AI.

But how does this conversation actually happen in our Next.js application? Where is this "backend" located, and how does the frontend (the code running in your browser) send requests to it?

This is where **Next.js API Routes** come in!

Think of API Routes as **special doors or communication channels** built right into your Next.js application. They allow the code running in the user's browser (the frontend) to send messages and data securely to the server-side part of your application, and then receive responses back.

When you need to:

*   Fetch a list of your forms ([Chapter 2](02_form_structure___management_.md))
*   Save a new form you just created (maybe with AI help from [Chapter 4](04_ai_integration_.md))
*   Submit answers to a public form ([Chapter 3](03_form_responses___analytics_.md))
*   Log in using your credentials ([Chapter 1](01_user_authentication___management_.md))
*   Analyze a document using AI ([Chapter 4](04_ai_integration_.md))

...your frontend code doesn't directly touch the database or call external APIs. Instead, it sends a request to one of these API Routes. The API Route then performs the necessary server-side operations (like talking to the [Database - Chapter 6](06_database_connection___models_.md) or external services) and sends the result back to the frontend.

They act as the **secure middle layer** between your user interface and your server-side logic and data.

## Where Do API Routes Live?

In Next.js, API Routes live inside the special `app/api` directory. Any file you create inside `app/api` will automatically become an API endpoint.

For example:

*   A file named `app/api/forms/route.ts` will handle requests sent to `/api/forms`.
*   A file named `app/api/atlas-auth/login/route.ts` will handle requests sent to `/api/atlas-auth/login` (like we saw in [Chapter 1](01_user_authentication___management_.md)).
*   A file named `app/api/forms/[id]/submit/route.ts` will handle requests sent to `/api/forms/some-form-id/submit`, where `[id]` is a dynamic part of the URL (the form's ID).

## How Do They Work? HTTP Methods

API Routes respond to standard HTTP methods like GET, POST, PUT, PATCH, and DELETE. These methods correspond to common actions you perform on data:

*   **GET:** Requesting data (e.g., "Get me the list of my forms", "Get me the details of form X").
*   **POST:** Creating new data (e.g., "Post this new form", "Post these submitted answers").
*   **PUT / PATCH:** Updating existing data (e.g., "Update form X with these changes").
*   **DELETE:** Removing data (e.g., "Delete form X").

Inside a `route.ts` file, you define async functions that match these methods. For example, in `app/api/forms/route.ts`, you'll find `async function GET(request)` to handle GET requests to `/api/forms` and `async function POST(request)` to handle POST requests to the same URL.

## Use Case: Fetching Your Forms via API Route

Let's revisit the example from [Chapter 2: Form Structure & Management](02_form_structure___management_.md) where the frontend fetches the user's forms.

From the client-side component (`app\forms\FormsClient.tsx`), we saw this simple `fetch` call:

```typescript
// Inside app\forms\FormsClient.tsx (Simplified frontend fetch)
const fetchForms = async () => {
  try {
    // This is the frontend talking to our backend API Route
    const response = await fetch('/api/forms');

    if (!response.ok) {
      // Handle errors (e.g., if the API Route returned 401 Unauthorized)
      throw new Error('Failed to fetch forms');
    }

    const data = await response.json(); // Get the JSON data from the server
    setForms(data); // Use the data to update the UI
  } catch (error) {
    console.error('Error fetching forms:', error);
    // Show error message to user
  }
};
```

This `fetch('/api/forms')` is the signal sent from the user's browser to our Next.js API Route server. Now, let's see how the API Route receives and handles this signal.

## Under the Hood: The `/api/forms` GET Route

When the `fetch('/api/forms')` request arrives at our Next.js server, the code inside `app/api/forms/route.ts` that handles the `GET` method takes over.

Here's a simplified walkthrough of what that API Route does:

```mermaid
sequenceDiagram
    participant UserBrowser as User (Browser)
    participant FormsClient as FormsClient (Client Component)
    participant FormsAPIRoute as Backend API (/api/forms GET)
    participant UserActions as User Actions (getCurrentUser)
    participant Database as Our Database (MongoDB)

    UserBrowser->>FormsClient: Visits My Forms page
    FormsClient->>FormsAPIRoute: GET request to /api/forms (sent by fetch)
    FormsAPIRoute->>UserActions: Calls getCurrentUser() (Checks identity)
    UserActions->>Database: Reads cookies, finds user (Chapter 1, 6)
    Database-->>UserActions: Returns User object
    UserActions-->>FormsAPIRoute: Returns User object or null
    alt If User is Logged In (UserActions returned User)
        FormsAPIRoute->>Database: Queries Forms for this user's ID (Chapter 2, 6)
        Database-->>FormsAPIRoute: Returns list of Forms
        FormsAPIRoute-->>FormsClient: Sends list of Forms (JSON) (NextResponse.json)
        FormsClient->>UserBrowser: Displays the forms
    else If User Not Logged In (UserActions returned null)
        FormsAPIRoute-->>FormsClient: Sends 401 Unauthorized (NextResponse)
        FormsClient->>UserBrowser: Shows error or redirects to login
    end
```

Let's look at the simplified code for this `GET` handler in `app\api\forms\route.ts`:

```typescript
// Inside app\api\forms\route.ts (Simplified GET handler)
import { NextResponse } from 'next/server'; // How we send responses
import { Form } from '@/app/models/Form'; // Our Form blueprint (Chapter 2, 6)
import connectDB from '@/lib/mongodb'; // Our database connection helper (Chapter 6)
import { getCurrentUser } from '@/lib/actions/user.actions'; // Helper to get user (Chapter 1)

// This function handles GET requests to /api/forms
export async function GET(request: any) {
  try {
    console.log('Received GET request for /api/forms');
    await connectDB(); // Connect to the database (essential for DB operations)

    // 1. Check who the logged-in user is (Authentication)
    console.log('Checking current user...');
    const user = await getCurrentUser(); // Uses cookies set during login (Chapter 1)

    // If no user is found, they are not logged in -> send Unauthorized response
    if (!user) {
      console.log('Unauthorized access attempt to /api/forms');
      // Send a JSON response with an error message and 401 status code
      return NextResponse.json(
        { error: 'Unauthorized - Please log in', redirectTo: '/login' },
        { status: 401 } // 401 means Unauthorized
      );
    }

    const userId = user._id; // Get the user's database ID
    const userEmail = user.email; // Get the user's email

    // 2. Fetch only the forms belonging to this user (Authorization & Database interaction)
    console.log(`Fetching forms for user: ${userEmail} (ID: ${userId})`);
    
    // Build a query to find forms where the userId or userEmail matches the current user
    const query: any = {
      $or: [
        { userEmail: userEmail },
      ]
    };
    if (userId) {
      query.$or.push({ userId: userId });
    }
     // Handle older records or different authentication systems if needed (like clerkId from context)
    if (user.clerkId) {
      query.$or.push({ userId: user.clerkId });
    }

    // Use our Mongoose model (Form) to query the database
    const forms = await Form.find(query).sort({ createdAt: -1 }); // Find and sort by creation date

    // 3. Send the forms back to the frontend
    console.log(`Found ${forms.length} forms. Sending response.`);
    // Send the list of forms as JSON with a 200 OK status (default for NextResponse.json)
    return NextResponse.json(forms);

  } catch (error: any) {
    console.error('Error fetching forms in API route:', error);
    // If something goes wrong, send a 500 Internal Server Error
    return NextResponse.json(
      { error: error.message || 'Error fetching forms' },
      { status: 500 } // 500 means Internal Server Error
    );
  }
}
```

Let's break this down:

1.  **`import { NextResponse } from 'next/server';`**: This line imports the special object we use to create responses that Next.js understands.
2.  **`import Form from '@/models/Form';`**: Imports our database model ([Chapter 6](06_database_connection___models_.md), [Chapter 2](02_form_structure___management_.md)) so we can interact with the forms collection in our database.
3.  **`import connectDB from '@/lib/mongodb';`**: Imports our helper function to ensure we're connected to the database before trying to use it ([Chapter 6](06_database_connection___models_.md)).
4.  **`import { getCurrentUser } from '@/lib/actions/user.actions';`**: Imports a function that checks the user's authentication status based on their cookie ([Chapter 1](01_user_authentication___management_.md)).
5.  **`export async function GET(request: any)`**: This is the core of the API route. `export` makes it available, `async` means it will do things that take time (like database calls), `function GET` tells Next.js to run this for GET requests, and `request` is an object containing details about the incoming request (like headers, body, URL).
6.  **`await connectDB();`**: Makes sure our database connection is ready.
7.  **`const user = await getCurrentUser();`**: Calls our authentication helper. This is a crucial step inside API routes that require a logged-in user.
8.  **`if (!user) { ... }`**: If `getCurrentUser` returns null, the user isn't logged in, so we immediately stop and return a `401 Unauthorized` response using `NextResponse.json()`. The `status: 401` is important for the frontend to understand *why* the request failed.
9.  **`const forms = await Form.find(query).sort({ createdAt: -1 });`**: This is where the API route does the actual work. It uses the `Form` model to find documents in the database, filtering by the `userId` or `userEmail` obtained from the authenticated user. It then sorts them. This is the core business logic for fetching *only* the user's forms.
10. **`return NextResponse.json(forms);`**: If everything worked, this line takes the `forms` data we got from the database and sends it back to the frontend as a JSON response. `NextResponse.json` automatically sets the content type and the status code (to 200 OK by default).
11. **`catch (error)`**: If any error happens during the process (database connection fails, query error, etc.), we catch it and return a `500 Internal Server Error` to the frontend.

This demonstrates the typical structure: receive request, authenticate/authorize, perform server-side logic (often involving the database), and send a structured response.

## Receiving Data: POST Requests

When the frontend needs to send data to the server (like submitting a form or sending a description to the AI), it typically uses a `POST` request. The data is usually sent in the "body" of the request, often as JSON.

The API route handler for POST requests receives this data via the `request` object.

Example from creating a form (`app\api\forms\route.ts` - POST):

```typescript
// Inside app\api\forms\route.ts (Simplified POST handler)
import { NextResponse } from 'next/server';
import { Form } from '@/app/models/Form';
import connectDB from '@/lib/mongodb';
import { getCurrentUser } from '@/lib/actions/user.actions';

// This function handles POST requests to /api/forms
export async function POST(request: any) {
  try {
    await connectDB();

    // 1. Get the current user (Authentication/Authorization)
    const user = await getCurrentUser();
    if (!user) {
       return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 2. Get the data sent from the frontend in the request body
    const formData:any = await request.json(); // <-- Get the JSON body here!
    console.log('Received form data:', { name: formData.name });

    // 3. Add user ID/email to the data (Association)
    formData.userId = user._id;
    formData.userEmail = user.email;
    // ... potentially add other user info or process fields ...

    // 4. Create a new form in the database (Database Interaction)
    const form = new Form(formData);
    await form.save();

    // 5. Send a success response back
    console.log('Form saved:', form._id);
    return NextResponse.json({ success: true, formId: form._id.toString() });

  } catch (error: any) {
    console.error('Error creating form:', error);
    return NextResponse.json(
      { error: error.message || 'Error creating form' },
      { status: 500 }
    );
  }
}
```

The key difference here is `const formData = await request.json();`. This line reads the incoming request body and parses it as JSON, making the data available in the `formData` variable. The rest of the process involves using this data, linking it to the user, saving it to the database ([Chapter 6](06_database_connection___models_.md)), and sending a response.

## Dynamic Routes

Sometimes, your API endpoint needs to include variable information in the URL, like the ID of a specific form you want to get, update, or delete. This is handled using square brackets `[]` in the file/folder name within the `app/api` directory.

Example: `app/api/forms/[id]/route.ts` handles requests like `/api/forms/abc123def456` or `/api/forms/xyz987`. The value inside the brackets (`abc123def456` or `xyz987`) becomes available in the API route handler.

```typescript
// Inside app\api\forms\[id]\route.ts (Simplified GET handler for a single form)
import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Form from '@/models/Form';
import { getCurrentUser } from '@/lib/actions/user.actions'; // Needed for ownership check

// This function handles GET requests to /api/forms/:id
export async function GET(
  request: Request, // Standard Request object
  context: { params: { id: string } } // <-- Dynamic segment 'id' is here
) {
  try {
    await connectDB();
    const formId = context.params.id; // Get the ID from the URL

    console.log(`Received GET request for form ID: ${formId}`);

    // Optional: Check if the user is logged in and owns this form (Authorization)
    // const user = await getCurrentUser();
    // if (!user) { /* ... handle unauthorized ... */ }
    // Check form ownership: find form by ID *AND* user ID
    // const form = await Form.findOne({ _id: formId, userId: user._id }).lean();
    // OR if it's a public form access (Chapter 3), just check if it's published

    // Simplified: Just find the form by ID
    const form = await Form.findById(formId).lean();

    if (!form) {
      console.warn(`Form ID ${formId} not found.`);
      return NextResponse.json({ error: 'Form not found' }, { status: 404 }); // 404 Not Found
    }

    console.log(`Form ${formId} found. Sending response.`);
    return NextResponse.json(form);

  } catch (error: any) {
    console.error('Error fetching single form:', error);
    return NextResponse.json(
      { error: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}

// Similar functions for PUT (update) and DELETE
// Inside PUT and DELETE handlers, you also access context.params.id
// and you'd typically check user ownership before performing the action.
```

Notice that the `context` parameter in the handler function receives an object with `params`, which contains the value extracted from the dynamic segment `[id]`. This is how you know *which* form the request is about.

## Why Use API Routes? (Benefits)

Now that you've seen them in action, let's summarize why Next.js API Routes are so important for Myform and applications like it:

1.  **Security:** They keep sensitive operations and data (like database credentials, API keys for AI or Atlas, complex validation logic) safely on the server, away from the user's browser where anyone could potentially see them. Calling external APIs like Atlas login or OpenAI/Gemini for AI features *must* happen on the server for security and often due to CORS restrictions.
2.  **Access to Server-Side Resources:** Only code running on the server can directly access your database ([Chapter 6](06_database_connection___models_.md)), read server-only environment variables (where you store secrets), or perform file system operations (though less common in API routes).
3.  **Abstraction and Organization:** They provide a clear separation between your frontend (UI) and your backend (data and logic). The frontend just needs to know *which* API endpoint to call and *what* data to send/expect, without knowing the details of *how* the server performs the task.
4.  **Performance:** For operations that require significant processing (like complex data analysis or AI model calls), performing them in an API Route on the server can be faster than trying to do it in the user's potentially less powerful browser.
5.  **Flexibility:** API Routes can be used by any client capable of making HTTP requests, not just your Next.js frontend.

## Connecting to Other Myform Concepts

API Routes are the glue that connects many pieces of Myform:

*   **[Chapter 1: User Authentication & Management](01_user_authentication___management_.md):** The login process happens in `/api/atlas-auth/login`, which interacts with the Atlas API and saves user data in *our* database. Other protected API routes use `getCurrentUser` and `checkAdminStatus` to ensure only authorized users can access them.
*   **[Chapter 2: Form Structure & Management](02_form_structure___management_.md):** API routes like `/api/forms` and `/api/forms/[id]` are used by the frontend to create, list, view, update, and delete forms, interacting directly with the `Form` database model.
*   **[Chapter 3: Form Responses & Analytics](03_form_responses___analytics_.md):** The public submission route `/api/forms/[id]/submit` handles saving form responses to the `FormResponse` database model. The dashboard route `/api/forms/[id]/dashboard` fetches and processes these responses for display.
*   **[Chapter 4: AI Integration](04_ai_integration_.md):** Routes like `/api/generate-form` and `/api/analyze-document` act as secure gateways to external AI services, handling API keys and translating AI output into our form structure.
*   **[Chapter 6: Database Connection & Models](06_database_connection___models_.md):** Almost every API route that handles data management directly interacts with the database using Mongoose models (`User`, `Form`, `FormResponse`).

## Conclusion

In this chapter, we demystified Next.js API Routes, understanding them as the essential communication layer between your frontend and backend. We saw how files in the `app/api` directory become API endpoints, how different HTTP methods map to handler functions (`GET`, `POST`, etc.), and how they receive data from the request and send data back in the response using `NextResponse`. We also highlighted their crucial role in security, accessing server resources, and connecting the various features of Myform, from user authentication and form management to handling responses and integrating AI.

API Routes are where the real "server-side work" gets done in a Next.js application. They are the backbone that allows our frontend to be dynamic and interact with persistent data and external services.

Now that we understand how the frontend talks to the backend, let's dive deeper into one of the most common things the backend talks to: the database!

[Chapter 6: Database Connection & Models](06_database_connection___models_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)