# Chapter 1: The AgenticRAG System

Welcome to the first chapter of the `atlas-q-a-rag` tutorial! We're excited to guide you through building a powerful system that can answer questions by intelligently using different tools and data sources.

In this chapter, we'll introduce the central piece of the puzzle: the **AgenticRAG System**.

## What Problem Does It Solve?

Imagine you want to build an application that can answer questions using various "expert" modules. For example:

*   An "Internal Docs Bot" that knows everything about your company's internal documents.
*   A "Web Search Bot" that can look up information on the internet.
*   A "Database Bot" that can query your customer database.

Each of these "bots" might work differently and require different tools. How do you build a single system that can:

1.  Know about all these different bots?
2.  Figure out which bot is best suited for a user's question?
3.  Send the question to the right bot?
4.  Get the answer back and present it to the user?

This is where the **AgenticRAG System** comes in!

## The Central Command Center

Think of the **AgenticRAG System** as the **central command center** or the **conductor of an orchestra**. Its main job is to orchestrate all the different parts of our system. It doesn't answer the questions itself, but it knows *who* can, *how* to reach them, and *how* to manage the overall flow.

Here are its key roles:

*   **Loading Bo<PERSON>:** It knows where to find the configurations for all the different "bots" you want to have (we'll talk more about the [Bot](02_bot_.md) concept in the next chapter!). It loads them up when the system starts.
*   **Initializing Tools:** Each bot might need specific tools (like a web search tool or a document search tool). The AgenticRAG System makes sure these [Tool](04_tool_.md)s are ready to be used.
*   **Receiving Queries:** When a user asks a question, it's the AgenticRAG System that first receives it.
*   **Directing Queries:** Based on the user's question and which bot they want to talk to, it directs the query to the appropriate bot's internal components, including the [Query Router](03_query_router_.md) and the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md).
*   **Overall Management:** It oversees the entire process from receiving the query to sending back the final answer.

It's essentially the main entry point for anyone wanting to interact with the system's core backend logic.

## How Do You Interact With It? (From the Outside)

From an external perspective (like a website or another application calling this system), you interact with the AgenticRAG System through an API. Looking at the `app/main.py` file, you can see it sets up a web server using FastAPI.

The most common interaction is asking a specific bot a question. This is handled by an endpoint that looks something like `/bots/{bot_name}/query`.

Here's a simplified look at that part of the code:

```python
# File: app\main.py

@app.post("/bots/{bot_name}/query", tags=["Queries"])
async def query_bot(
    bot_name: str, request: QueryRequest, rag: AgenticRAG = Depends(get_agentic_rag)
):
    """Query a specific bot."""
    try:
        # Get the AgenticRAG instance (the command center)
        # ... get the specific bot ...

        # Ask the AgenticRAG instance to process the query
        response = await rag.process_query(bot_name, request)

        # ... format and return the response ...

    # ... error handling ...
```

**Explanation:**

*   `@app.post("/bots/{bot_name}/query", ...)`: This line tells the web server that when someone sends a POST request to an address like `/bots/internal_docs_bot/query`, this `query_bot` function should run. `{bot_name}` is a placeholder for the name of the bot you want to query (like `internal_docs_bot`).
*   `async def query_bot(...)`: This defines the function that handles the request. It takes the `bot_name`, the user's `request` (which includes the actual question and maybe a session ID), and the `AgenticRAG` instance (`rag`).
*   `response = await rag.process_query(bot_name, request)`: This is the key line! It shows that the `main.py` file simply takes the request and passes it to the `AgenticRAG` instance's `process_query` method. It tells the `rag` system *which* bot to use (`bot_name`) and *what* the user asked (`request`).

So, from the outside, you just need to know the name of the bot you want to query and provide your question. The `AgenticRAG` system handles everything else internally.

## What Happens Inside the AgenticRAG System? (Under the Hood)

Let's peek inside the `app/core/agentic_rag.py` file to see how the AgenticRAG System works its magic.

### The Startup Process

When the system starts, the `AgenticRAG` class is initialized.

```python
# File: app\core\agentic_rag.py

class AgenticRAG:
    def __init__(self, config_dir: str = "configs", prompts_dir: str = "prompts"):
        """
        Initialize the AgenticRAG system.
        """
        self.config_dir = config_dir
        self.prompts_dir = prompts_dir
        # Creates a ConfigLoader (we'll cover config in Chapter 7)
        self.config_loader = ConfigLoader(config_dir)
        self.bots: Dict[str, Dict[str, Any]] = {} # This will store our loaded bots

        # Load all bot configurations and set them up!
        self._load_bots()

    def _load_bots(self) -> None:
        """Load all bot configurations and initialize tools."""
        bot_configs = self.config_loader.get_all_configs() # Get all configs

        for bot_name, bot_config in bot_configs.items():
            try:
                # Initialize tools for this bot
                tools = self._initialize_tools(bot_config)

                # Load prompts for this bot
                # ... _load_prompt calls ...

                # Initialize the query router for this bot
                query_router = QueryRouter(bot_config, tools)

                # Initialize the agent for this bot
                agent = LangGraphAgent(bot_config.agent, system_prompt, query_prompt)

                # Store all the pieces for this bot
                self.bots[bot_name] = {
                    "config": bot_config,
                    "tools": tools,
                    "query_router": query_router,
                    "agent": agent,
                }
                logger.info(f"Loaded bot: {bot_name}")
            except Exception as e:
                logger.error(f"Error loading bot {bot_name}: {str(e)}")
```

**Explanation:**

*   The `__init__` method is called when an `AgenticRAG` object is created. It sets up where to find configuration files and then immediately calls `_load_bots()`.
*   The `_load_bots()` method is crucial. It reads all the configuration files, and **for each bot**, it does the following:
    *   It calls `_initialize_tools` to prepare the specific [Tool](04_tool_.md)s that bot needs.
    *   It loads the text prompts the bot will use.
    *   It creates a [Query Router](03_query_router_.md) specifically for this bot, giving it access to the bot's configuration and tools.
    *   It creates an [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) for this bot, providing its specific configuration and prompts.
    *   Finally, it stores all these components (config, tools, router, agent) together in a dictionary called `self.bots`, keyed by the bot's name.

So, when the system starts, the AgenticRAG System prepares *all* the defined bots and their components, making them ready to handle queries.

### Processing a Query Step-by-Step

When `process_query(bot_name, request)` is called (as we saw in `main.py`), here's a simplified sequence of events:

```mermaid
sequenceDiagram
    participant User
    participant FastAPIA as FastAPI App
    participant AgenticRAG as AgenticRAG System
    participant BotComponent as Specific Bot Components
    participant QueryRouter as Query Router
    participant Agent as Agent

    User->>FastAPIA: Query Request (/bots/{name}/query)
    FastAPIA->>AgenticRAG: process_query(bot_name, request)
    AgenticRAG->>AgenticRAG: Get specified Bot's components
    AgenticRAG->>QueryRouter: route_query(user_query)
    QueryRouter-->>AgenticRAG: Tool selection & results
    AgenticRAG->>Agent: process_query(user_query, tool_results)
    Agent-->>AgenticRAG: Final response text
    AgenticRAG->>FastAPIA: QueryResponse (formatted)
    FastAPIA->>User: JSON Response
```

**Explanation of the flow:**

1.  The **User** sends a query request to the **FastAPI App**.
2.  The **FastAPI App** calls the `process_query` method on the **AgenticRAG System**, specifying the bot name and the query.
3.  The **AgenticRAG System** looks up the requested bot's components (`query_router`, `agent`, etc.) that it loaded during startup.
4.  It then passes the user's query to the bot's specific **[Query Router](03_query_router_.md)**. The router's job is to figure out *if* any tools are needed and potentially execute them.
5.  The **[Query Router](03_query_router_.md)** returns information back to the **AgenticRAG System**, including potentially the results from any tools that were used.
6.  The **AgenticRAG System** then takes the original query and the tool results (if any) and passes them to the bot's specific **[Agent (LangGraphAgent)](05_agent__langgraphagent__.md)**. The agent is responsible for using this information to generate the final answer.
7.  The **[Agent (LangGraphAgent)](05_agent__langgraphagent__.md)** returns its final response text.
8.  The **AgenticRAG System** formats the response, including the answer text and information about which tools were used, into a standard `QueryResponse` object.
9.  The **AgenticRAG System** returns this formatted response to the **FastAPI App**.
10. The **FastAPI App** sends the JSON response back to the **User**.

### Processing a Query (Code Dive)

Let's look at the `process_query` method in `app/core/agentic_rag.py` again, focusing on the core logic after the bot is found:

```python
# File: app\core\agentic_rag.py

    async def process_query(
        self, bot_name: str, request: QueryRequest
    ) -> QueryResponse:
        """
        Process a query for a specific bot.
        """
        bot = self.get_bot(bot_name)
        if not bot:
            raise ValueError(f"Bot not found: {bot_name}")

        try:
            # 1. Route the query using the bot's query router
            query_router: QueryRouter = bot["query_router"]
            tool_results = await query_router.route_query(
                request.query, **request.metadata or {}
            )

            # 2. Process the query and tool results with the bot's agent
            agent: LangGraphAgent = bot["agent"]
            agent_response = await agent.process_query(
                request.query,
                tool_results["tool_responses"], # Pass results from router
                session_id=request.session_id, # Pass session for memory
            )

            # 3. Format the response using results from agent and router
            # ... tool response formatting ...

            response = QueryResponse(
                bot_name=bot_name,
                query=request.query,
                response=agent_response["response"], # Get final text from agent
                tool_responses=tool_responses, # Include formatted tool results
                # ... include other metadata from router ...
                session_id=request.session_id,
                # ... include potential errors ...
            )

            return response
        except Exception as e:
            # ... error handling ...
            pass # Simplified for example
```

**Explanation:**

1.  `bot = self.get_bot(bot_name)`: It first retrieves the collection of components (config, tools, router, agent) for the requested bot name from the `self.bots` dictionary.
2.  `query_router.route_query(...)`: It calls the `route_query` method on the bot's specific [Query Router](03_query_router_.md) instance. This is where the decision-making about using tools happens.
3.  `agent.process_query(...)`: It then calls the `process_query` method on the bot's specific [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) instance, passing the original query and the results obtained from the router (which might include tool outputs). It also passes the `session_id`, which is important for [Memory Management](06_memory_management_.md).
4.  Finally, it constructs a `QueryResponse` object using the final text generated by the agent (`agent_response["response"]`) and the details about tool usage from the router's results (`tool_results["tool_responses"]`).

This method clearly shows the AgenticRAG System's role: getting the right bot components and coordinating the call flow between the [Query Router](03_query_router_.md) and the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md).

## Conclusion

In this chapter, we've learned that the **AgenticRAG System** is the heart of our application. It's the central orchestrator that loads and manages different bots, initializes their tools, receives user queries, and directs them to the appropriate bot components ([Query Router](03_query_router_.md) and [Agent (LangGraphAgent)](05_agent__langgraphagent__.md)) for processing. It provides the main entry point for interacting with the system.

Now that we understand the role of the central command center, let's dive into the building blocks it manages. In the next chapter, we'll explore the concept of a **Bot**.

[Bot](02_bot_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)