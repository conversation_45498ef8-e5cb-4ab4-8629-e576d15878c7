# Chapter 6: Atlas University Integration

Welcome back! In our previous chapters, we've built up a picture of the `mytask` application:
*   We learned about the [User and Authentication System](01_user_and_authentication_system_.md) and how users log in and are identified.
*   We explored the [Task Management Core](02_task_management_core_.md), the heart of the application where tasks live.
*   We saw how the frontend manages data centrally using [Frontend State Management (Redux Toolkit)](03_frontend_state_management__redux_toolkit__.md).
*   We dove into [API Communication (RTK Query)](04_api_communication__rtk_query__.md), understanding how the frontend talks to the backend.
*   And most recently, in [API Endpoints and Controllers (Backend)](05_api_endpoints_and_controllers__backend__.md), we saw how our backend receives and processes those requests.

Now, let's look at a special part of the `mytask` project: its connection to the **Atlas University systems**. This is crucial because `mytask` is designed *for* Atlas University members, and we want to make logging in and getting started as easy as possible for them.

### What is Atlas University Integration?

Imagine `mytask` needs to know who you are, but you already have an official ID card (your Atlas University credentials). Instead of making you get *another* ID card just for `mytask`, wouldn't it be great if you could use your existing one?

The **Atlas University Integration** module is the part of `mytask` that makes this possible. It acts as a secure bridge between our application and the official Atlas University systems.

Its main jobs are:

1.  **Authentication:** Allowing users to log in to `mytask` using their standard Atlas University username and password, without needing a separate password just for our app.
2.  **Synchronization:** When a user logs in via Atlas for the first time (or after their university information changes), fetching their basic details (like name, email, department) from the Atlas system and saving or updating this information in our `mytask` database.

This integration streamlines the login process and keeps essential user information (like which department someone is in) automatically updated in our app.

### Our Goal: Logging In with Atlas Credentials

Let's focus on the core use case: a user opening the `mytask` login page and successfully logging in using their Atlas username and password. We touched upon this in [Chapter 1: User and Authentication System](01_user_and_authentication_system_.md), but now we'll look specifically at how the Atlas connection works.

### Logging In (The User Experience Revisited)

As we saw in Chapter 1, the user enters their username and password into a form on the login page (`Login.jsx`). The crucial difference here is that these are their *Atlas* credentials.

```jsx
// client\src\pages\Login.jsx (Simplified)
import { useForm } from "react-hook-form";
import { Button, Textbox } from "../components";
// *** Import the specific RTK Query hook for server-side Atlas processing ***
import { useProcessAtlasLoginMutation } from "../redux/slices/api/atlasServerApiSlice";
// ... other imports ...

const Login = () => {
  const { register, handleSubmit } = useForm();
  // *** Get the mutation hook to call our backend endpoint ***
  const [processAtlasLogin, { isLoading }] = useProcessAtlasLoginMutation();
  // ... other state and hooks ...

  const handleAtlasLogin = async (data) => {
    try {
      console.log(`Attempting Atlas login via server for username: ${data.username}`);
      // *** Send username and password to OUR backend for Atlas processing ***
      const response = await processAtlasLogin({
        username: data.username,
        password: data.password,
      }).unwrap(); // .unwrap() gets the actual response data

      console.log("Login successful via server!", response);

      // The server response contains user data and our app's JWT
      // Dispatch action to save user data in Redux state
      // dispatch(setCredentials(response.user)); // Example, actual code slightly different

      // ... store user data and navigate (handled by Redux and router) ...

    } catch (err) {
      console.error("Login failed:", err);
      // Show an error message to the user
    }
  };

  return (
    <div className="...">
      <form onSubmit={handleSubmit(handleAtlasLogin)}>
        <Textbox label="Username" {...register("username")} icon={<FaUser className="text-gray-400" />} />
        <Textbox label="Password" type="password" {...register("password")} icon={<FaLock className="text-gray-400" />} />
        <Button type="submit" label="Login with Atlas" isLoading={isLoading} icon={<FaUniversity />} />
      </form>
      {/* ... error display, language selector ... */}
    </div>
  );
};

export default Login;
```
*   The frontend uses `useProcessAtlasLoginMutation` from `atlasServerApiSlice.js`. This is an RTK Query hook ([API Communication (RTK Query)](04_api_communication__rtk_query__.md)).
*   When the form is submitted, `handleAtlasLogin` is called.
*   Inside `handleAtlasLogin`, `processAtlasLogin({ username: data.username, password: data.password })` is called. This sends the Atlas credentials to a specific endpoint on *our backend server*. It does *not* send them directly to the external Atlas API from the user's browser. This is a security measure.
*   `isLoading` tracks if the request is in progress.
*   If the backend responds successfully (`response`), it means the backend successfully authenticated the user with Atlas, updated our database, and gave the frontend our application's token and the user's details.

### Behind the Scenes: How Atlas Integration Works (Backend)

The real magic of the Atlas integration happens on the backend. When our backend receives the `POST` request to `/api/atlas-auth/process-login` from the frontend, it orchestrates the communication with the external Atlas systems and our own database.

Here's a simplified flow:

```mermaid
sequenceDiagram
    participant Frontend as Frontend (Browser)
    participant OurBackend as Our Backend Server
    participant AtlasAPI as External Atlas API
    participant OurDatabase as Our Database (MongoDB)

    Frontend->>OurBackend: POST /api/atlas-auth/process-login<br>(Username, Password)
    OurBackend->>OurBackend: Receive Request
    OurBackend->>AtlasAPI: POST /Login/Login<br>(Username, Password)
    AtlasAPI-->>OurBackend: Response (Success/Fail, Atlas Token)
    alt Atlas Login Successful
        OurBackend->>AtlasAPI: GET /Login/LoginData<br>(using Atlas Token)
        AtlasAPI-->>OurBackend: Response (Atlas User Data)
        OurBackend->>OurBackend: Process Atlas User Data
        OurBackend->>OurDatabase: Find User by Atlas ID/GUID/etc.
        alt User Found in Our Database
            OurDatabase-->>OurBackend: Existing User Data
            OurBackend->>OurDatabase: Update/Sync User Data<br>(Name, Email, Dept, Photo, etc.)
            OurDatabase-->>OurBackend: Confirmation
        else User NOT Found
            OurBackend->>OurDatabase: Create New User<br>(using Atlas Data)
            OurDatabase-->>OurBackend: New User Data
        end
        OurBackend->>OurBackend: Generate Our App's JWT
        OurBackend-->>Frontend: Our App's JWT + Synced User Data
    else Atlas Login Failed
        OurBackend-->>Frontend: Error Response (e.g., 401 Unauthorized)
    end
```

1.  The **Frontend** sends the Atlas username and password to **Our Backend Server** at a specific endpoint (`/api/atlas-auth/process-login`).
2.  **Our Backend** receives the request in the corresponding **Controller** function ([API Endpoints and Controllers (Backend)](05_api_endpoints_and_controllers__backend__.md)).
3.  The controller uses a library (like Axios) to make an *outbound* `POST` request to the **External Atlas API**'s login endpoint, sending the username and password provided by the user.
4.  The **Atlas API** processes this request and sends a response back to our backend. If successful, this response includes an Atlas-specific authentication token (`webToken` in the code).
5.  If the Atlas login was successful, our backend then uses the Atlas token it just received to make *another* outbound `GET` request to the **Atlas API**'s user data endpoint (`/Login/LoginData`). This second call retrieves detailed profile information about the user (name, email, department, etc.).
6.  Our backend receives this **Atlas User Data**.
7.  It then checks its **Our Database** ([Database Models](07_database_models_.md)) to see if a user with this Atlas identity (using unique identifiers like `atlasUserId` or `guid`) already exists in our system.
8.  *If* the user is found, our backend **synchronizes** their data by updating their record in our database with the latest information from Atlas (name, email, department, etc.).
9.  *If* the user is *not* found, our backend **creates a new user record** in our database using the information received from the Atlas API. This new record is marked as an external Atlas user and linked to their Atlas ID/GUID.
10. Regardless of whether the user was found or created, our backend then generates our application's own **JWT token** ([User and Authentication System](01_user_and_authentication_system_.md)), which the frontend will use for subsequent requests to `mytask`.
11. Finally, our backend sends a success response back to the **Frontend**, including our app's JWT token and the user's synced data from our database.

If any step fails (Atlas login invalid, Atlas user data unavailable, database error), our backend sends an appropriate error response back to the frontend.

### Code Deep Dive: Backend Integration

Let's look at the key parts of the backend code responsible for this flow, located primarily in `server\controllers\atlasAuthController.js`.

First, the `processAtlasLogin` controller function:

```javascript
// server\controllers\atlasAuthController.js (Simplified processAtlasLogin)
import asyncHandler from "express-async-handler";
import axios from "axios"; // Library to make HTTP requests
import User from "../models/userModel.js"; // Our User database model
import createJWT from "../utils/index.js"; // Utility to create our app's JWT
import { ATLAS_API_ENDPOINTS } from "../config/apiConfig.js"; // Config with Atlas API URLs

const processAtlasLogin = asyncHandler(async (req, res) => {
  const { username, password } = req.body; // Get credentials from frontend request

  // --- Step 1: Authenticate with Atlas API ---
  console.log('Step 1: Authenticating with Atlas API...');
  let loginResponse;
  try {
    loginResponse = await axios.post(ATLAS_API_ENDPOINTS.LOGIN, {
      username,
      password,
      aplicationName: "MYATLAS", // App identifier required by Atlas API
      isMobile: false
    });
    if (!loginResponse.data || !loginResponse.data.webToken) {
       throw new Error('Invalid response from Atlas API login');
    }
    const token = loginResponse.data.webToken; // Get Atlas token
    console.log('Atlas token received.');

    // --- Step 2: Get user data from Atlas API using token ---
    console.log('Step 2: Fetching Atlas user data...');
    const atlasUserData = await axios.get(ATLAS_API_ENDPOINTS.LOGIN_DATA, {
        headers: { 'Authorization': `Bearer ${token}` } // Send Atlas token in header
    });
    if (!atlasUserData.data) {
        throw new Error('Invalid response from Atlas API user data');
    }
    const userData = atlasUserData.data;
    console.log('Atlas user data received.');

    // --- Step 3: Find or create user in our database & Synchronize ---
    console.log('Step 3: Syncing user with our database...');
    // Extract key fields from Atlas data (check different possible names)
    const userId = userData.userId || userData.id || userData.atlasUserId;
    const email = userData.email || userData.Email;
    const name = userData.nameSurname || userData.name;
    const samAccountName = username; // Use username for SAMAccountName

    // Find existing user using Atlas ID, GUID, or username/email
    let user = await User.findOne({
        $or: [{ atlasUserId: userId }, { guid: userData.guid }, { username: samAccountName }, { email: email }]
    });

    if (user) {
      console.log(`Found existing user: ${user.name}. Updating...`);
      // Update fields from Atlas data (synchronization)
      user.name = name || user.name;
      user.email = email || user.email;
      user.title = userData.jobTitle || userData.title || user.title;
      user.department = userData.department || user.department;
      user.company = userData.company || user.company;
      user.photoReference = userData.photoReference || user.photoReference;
      user.atlasUserId = userId || user.atlasUserId; // Ensure Atlas ID is stored
      user.guid = userData.guid || user.guid; // Ensure GUID is stored
      user.lastLogin = new Date();
      user.hasToken = true; // Mark as having a valid token

      await user.save({ validateBeforeSave: false }); // Save updates
      console.log('User updated.');

    } else {
      console.log('User not found. Creating new user...');
      // Create new user using Atlas data
      user = await User.create({
        name: name || 'New Atlas User',
        email: email || `${samAccountName}@atlas.edu.tr`, // Ensure email
        username: samAccountName,
        title: userData.jobTitle || userData.title || 'Öğrenci',
        department: userData.department,
        company: userData.company,
        photoReference: userData.photoReference,
        atlasUserId: userId,
        guid: userData.guid,
        password: undefined, // No password for Atlas users
        isAdmin: false, // Atlas users start as non-admins
        isActive: true,
        hasToken: true,
        lastLogin: new Date()
      });
      console.log('New user created.');
    }

    // --- Step 4: Generate our app's JWT and send response ---
    console.log('Step 4: Generating JWT and sending response...');
    createJWT(res, user._id); // Create and set JWT cookie

    // Prepare user object for frontend (remove sensitive info)
    const userObject = user.toObject();
    delete userObject.password;

    res.status(200).json({
      status: true,
      message: 'Login successful',
      user: userObject, // Synced user data from our DB
      atlasToken: token // Optionally send Atlas token if needed client-side
    });

  } catch (error) {
    console.error('Atlas login processing error:', error.message);
    // Log detailed error response from Atlas API if available
    if (error.response) {
       console.error('Atlas API Response:', error.response.status, error.response.data);
    }
    const errorMessage = error.response?.data?.message || error.message || 'An unknown error occurred during Atlas login.';
    res.status(error.response?.status || 500).json({
        status: false,
        message: errorMessage
    });
  }
});

// ... other controller functions ...

export { processAtlasLogin };
```
*   The function uses `asyncHandler` for easier error handling with `async`/`await`.
*   It uses the `axios` library to make HTTP requests *from our backend* to the Atlas API endpoints defined in `ATLAS_API_ENDPOINTS`.
*   It first makes a `POST` call to `ATLAS_API_ENDPOINTS.LOGIN` with the username and password.
*   If that succeeds, it extracts the `webToken` and makes a `GET` call to `ATLAS_API_ENDPOINTS.LOGIN_DATA`, passing the token in the `Authorization` header, to get the user's profile details.
*   It then uses the `User` model ([Database Models](07_database_models_.md)) and methods like `findOne` to search for an existing user in our MongoDB database based on unique identifiers from the Atlas data (like `userId`, `guid`, or `username`/`email`).
*   Based on whether a user is found, it either updates the existing user's record with the latest Atlas data (`user.save()`) or creates a new user record (`User.create()`). This is the **synchronization step**.
*   Finally, it calls `createJWT(res, user._id)` to generate our application's JWT (which gets set as a cookie on the user's browser) and sends a success response back to the frontend, including the updated user data from our database.

The specific configuration for the Atlas API endpoints is kept in a configuration file (`server\config\apiConfig.js` or similar), making it easy to update if the Atlas API URLs change:

```javascript
// server\config\apiConfig.js (Simplified)
// ... server API endpoints config ...

// Configuration for the external Atlas University API
export const ATLAS_API_ENDPOINTS = {
  BASE_URL: "https://atlas-external-api.example.com/api", // Example Atlas API base URL
  LOGIN: "https://atlas-external-api.example.com/api/Login/Login", // Full login endpoint URL
  LOGIN_DATA: "https://atlas-external-api.example.com/api/Login/LoginData", // Full user data endpoint URL
  // ... other Atlas endpoints if needed ...
};

// Helper function to get full server URL (used client-side)
export const getFullServerUrl = (endpoint) => {
  // Logic to combine base URL and endpoint, possibly considering environment
  // return process.env.REACT_APP_SERVER_URL + endpoint;
  return `https://apimytask.atlas.edu.tr/api${endpoint}`; // Example
};

// ... other configurations ...
```

*   This centralizes the external API URLs, making the controller code cleaner.

Finally, this controller function is linked to the specific API endpoint in the backend routes, typically in a file like `server\routes\atlasAuthRoutes.js`:

```javascript
// server\routes\atlasAuthRoutes.js (Simplified)
import express from 'express';
import { processAtlasLogin } from '../controllers/atlasAuthController.js'; // Import the controller

const router = express.Router();

// Define the POST endpoint for processing Atlas login
// When a POST request hits /api/atlas-auth/process-login, call processAtlasLogin
router.post('/process-login', processAtlasLogin);

// ... other Atlas related routes if any ...

export default router;
```
*   This snippet shows how the `/process-login` path is defined within a router specific to Atlas authentication, and how it's connected to the `processAtlasLogin` controller function.

This backend setup ensures that the complex interaction with the external Atlas API, the fetching of user data, and the crucial synchronization step happen securely on the server, shielded from the frontend browser.

### Synchronization in More Detail

The synchronization process is key. When a user logs in via Atlas:

*   We try to find their record in our `mytask` database using unique Atlas identifiers (`atlasUserId`, `guid`). These fields are added to our `User` model specifically for this integration ([Database Models](07_database_models_.md)).
*   If found, we update certain fields in our database record with the latest data from Atlas, such as `name`, `email`, `department`, `company`, and `photoReference`. This keeps our user information current.
*   If not found, we create a *new* user record in our database. This record includes their basic Atlas info and the Atlas identifiers, but also app-specific fields like `role` (initially 'user'), `isActive` (true), and `isAdmin` (false by default). We only store a dummy `password` or `undefined` because authentication is handled by Atlas. The `isExternal: true` flag in the `User` model helps identify these users.

This approach means we don't have to rely on calling the Atlas API for basic user information every time, improving performance. It also allows us to link tasks and other app data to these users and manage app-specific settings (like `isAdmin`) independently in our database.

### Connecting Frontend and Backend with RTK Query

As seen in the `Login.jsx` snippet, the frontend uses `useProcessAtlasLoginMutation`. This hook is generated by RTK Query based on the definition in `client\src\redux\slices\api\atlasServerApiSlice.js`.

```javascript
// client\src\redux\slices\api\atlasServerApiSlice.js (Simplified)
import { apiSlice } from "../apiSlice"; // Our base API slice

// Inject endpoints related to server-processed Atlas interactions
export const atlasServerApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Define a mutation endpoint to call our backend's Atlas login processor
    processAtlasLogin: builder.mutation({
      query: (data) => ({ // data contains username, password from the form
        url: `/atlas-auth/process-login`, // The backend endpoint URL
        method: "POST", // Use POST to send credentials
        body: data, // Send the username/password as the request body
        credentials: "include", // Ensure cookies (our JWT) are handled
      }),
      // Optionally invalidate relevant cache tags if needed (e.g., user data)
      // invalidatesTags: ['User'], // Could be used to refresh logged-in user info
    }),
    // ... other server-processed Atlas endpoints if any ...
  }),
});

// Export the generated hook
export const { useProcessAtlasLoginMutation } = atlasServerApiSlice;
```
*   This slice defines the `processAtlasLogin` mutation.
*   Its `query` function specifies the URL (`/atlas-auth/process-login` relative to our backend API base URL), the `POST` method, and sets the request body (`data`).
*   The `useProcessAtlasLoginMutation` hook generated from this definition is what the `Login.jsx` component calls, abstracting away the underlying HTTP request details ([API Communication (RTK Query)](04_api_communication__rtk_query__.md)).

### Conclusion

In this chapter, we delved into the **Atlas University Integration**, understanding how it allows users to log in using their Atlas credentials. We saw that the backend server plays a central role, communicating securely with the external Atlas API to authenticate the user and fetch their data, and then performing the crucial **synchronization** step by finding or creating the user in our own database. This process ensures a seamless login experience and keeps essential user information up-to-date within `mytask`, linking it to tasks and other app features.

We saw how the frontend uses a dedicated RTK Query hook to initiate this process by sending credentials to our backend, and how the backend controller orchestrates the outbound calls to the external Atlas API and the interactions with our internal database ([Database Models](07_database_models_.md)).

Understanding this integration highlights the flow of information not just between frontend and backend, but also between our backend and external systems. This synchronization relies heavily on the structure of the data we store for users in our database.

In the next chapter, we'll explore **Database Models** in more detail, examining how data like users and tasks are structured and stored in MongoDB, which is fundamental to all backend operations, including the user synchronization we discussed here.

[Database Models](07_database_models_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)