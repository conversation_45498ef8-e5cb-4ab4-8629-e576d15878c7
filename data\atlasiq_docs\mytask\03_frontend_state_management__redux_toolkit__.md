# Chapter 3: Frontend State Management (Redux Toolkit)

Welcome back, future `mytask` developer! In [Chapter 1: User and Authentication System](01_user_and_authentication_system_.md), we learned how users securely log into our application. In [Chapter 2: Task Management Core](02_task_management_core_.md), we explored how to create and view tasks.

Now, let's think about how the *frontend* (the part of the application the user sees and interacts with in their browser) keeps track of all this important information. When a user logs in, the frontend needs to remember who they are. When they create or fetch tasks, the frontend needs to store that list of tasks to display it. What if different parts of your app need to know the logged-in user's name, or need to see the same list of tasks?

If each component fetched or stored this data independently, things would get messy fast! It would be hard to keep data consistent, components wouldn't know when other components changed data, and debugging would be a nightmare.

This is where **Frontend State Management** comes in.

### What is Frontend State Management?

Imagine your frontend application is a bustling office. Different departments (components) need access to important documents (data) like the list of all projects (tasks) or who the boss is (logged-in user). If everyone kept their own copies of these documents, they'd quickly get out of sync.

Frontend State Management is like setting up a **central, organized library** for your application's data. Instead of data being scattered around, it lives in one predictable place.

*   Any part of the application that needs data can **read** from the library.
*   When data needs to change (like adding a new task or logging out a user), changes are made in a controlled way through the library's defined process.
*   Other parts of the application are **notified** when the data changes, so they can update what they show.

This makes your application's data flow much easier to understand, manage, and predict.

In the `mytask` project, we use **Redux Toolkit** for this central library. It's the recommended way to use Redux, providing tools that simplify the setup and make it easier to write good state management code.

### Our Goal: Managing User Data and Tasks

Let's revisit our examples from previous chapters:

1.  **User Login:** When a user logs in, we get their user information back from the backend. The frontend needs to store this information so that *any* component (the header, the profile page, the task list filtering) can access it.
2.  **Task List:** When we fetch tasks from the backend, the frontend needs to store this list. The Board View, List View, and Tree View components all need access to the same list of tasks.

Redux Toolkit is how we achieve this central storage and access.

### Key Concepts of Redux Toolkit (Simplified)

Let's break down the core ideas behind Redux Toolkit using our library analogy:

*   **Store:** This is the **entire library building**. It holds *all* the state for your application. In code, it's a single JavaScript object.

*   **State:** This is the **actual data** inside the library. It's the collection of all documents, sorted and organized. In code, `state` is the data structure (like objects and arrays) within the store. Example: `{ user: { name: '...', email: '...' }, tasks: [...], isLoading: false }`.

*   **Actions:** When you want something to happen in the library (like checking out a book or adding a new document), you fill out a **request slip**. An action is a plain JavaScript object that *describes* what happened. It must have a `type` field (like 'user/loginSuccess') and can have a `payload` field with any relevant data (like the user's details).

    ```javascript
    // Example Action object
    {
      type: 'auth/setCredentials', // Describes the type of event
      payload: { name: 'Alice', email: '<EMAIL>', _id: 'user123' } // The data associated with the event
    }
    ```
    An action doesn't *do* anything itself; it just signals that something occurred.

*   **Dispatch:** This is the act of **handing your request slip to the librarian**. It's a function provided by the Redux store. You call `dispatch(action)` to send an action, signaling that a state change might be needed.

    ```javascript
    // In a component, after a successful login API call
    import { useDispatch } from 'react-redux';
    import { setCredentials } from '../redux/slices/authSlice'; // Our 'request slip' creator

    const dispatch = useDispatch();
    // After backend confirms login and gives user data:
    const userData = response; // Assume response contains user data
    dispatch(setCredentials(userData)); // Hand the request slip to the librarian!
    ```
    This tells Redux, "Hey, the user logged in, here's their data!"

*   **Reducers:** This is the **librarian** who processes your request slip. A reducer is a function that takes the *current state* and the *action* you dispatched, and based on the action's `type`, it figures out *how* to update the state. Reducers *never* change the existing state directly; they return a *new* state object with the changes applied.

    ```javascript
    // Simplified Reducer logic for 'auth/setCredentials'
    function authReducer(currentState, action) {
      if (action.type === 'auth/setCredentials') {
        // Return a *new* state object
        return {
          ...currentState, // Copy all existing state properties
          user: action.payload, // Update the 'user' property with the new data
        };
      }
      // If the action type doesn't match, return the current state unchanged
      return currentState;
    }
    ```
    Reducers are the *only* way state changes in Redux. This makes state changes predictable and traceable.

*   **Selectors:** This is how you **read data** from the library. You don't dig through the shelves yourself; you ask the librarian for a specific document. Selectors are functions that receive the entire Redux state and return a specific piece of data from it.

    ```javascript
    // In a component that needs the logged-in user's name
    import { useSelector } from 'react-redux';

    const user = useSelector((state) => state.auth.user); // Ask the librarian for the 'user' data in the 'auth' section

    // Now you can use user.name in your component
    ```
    Using selectors is the standard way to get data from the Redux store into your React components.

*   **Slices (Redux Toolkit Magic):** Redux Toolkit introduces `createSlice`. This simplifies defining actions and reducers by combining them into one place, managing a specific "slice" of your state (like the 'auth' slice for user data, or a 'tasks' slice for task data). `createSlice` automatically generates action creators for you.

    ```javascript
    // Simplified createSlice example for auth
    import { createSlice } from '@reduxjs/toolkit';

    const authSlice = createSlice({
      name: 'auth', // Prefix for action types (e.g., 'auth/setCredentials')
      initialState: { user: null }, // Initial state for this slice
      reducers: { // Functions to handle state updates
        setCredentials: (state, action) => {
          // Redux Toolkit uses Immer library, allowing "mutating" syntax
          // Under the hood, Immer creates an immutable update for you!
          state.user = action.payload;
        },
        logout: (state) => {
          state.user = null;
        }
      }
    });

    export const { setCredentials, logout } = authSlice.actions; // Generated action creators
    export default authSlice.reducer; // The combined reducer function
    ```
    `createSlice` dramatically reduces boilerplate code and makes Redux development much cleaner.

### Managing User State with Redux Toolkit

Let's see how the logged-in user data is managed using an `authSlice`.

From the code snippet in [Chapter 1](01_user_and_authentication_system_.md), we saw the `authSlice.js` file:

```javascript
// client\src\redux\slices\authSlice.js (Simplified)
import { createSlice } from "@reduxjs/toolkit";

// Define the initial state for the authentication slice
const initialState = {
  user: localStorage.getItem("userInfo")
    ? JSON.parse(localStorage.getItem("userInfo"))
    : null,
  atlasAuth: localStorage.getItem("atlasAuth")
    ? JSON.parse(localStorage.getItem("atlasAuth"))
    : null,
  isSidebarOpen: false,
  authProvider: localStorage.getItem("authProvider") || "local",
};

// Create the auth slice using createSlice
const authSlice = createSlice({
  name: "auth", // This name is used as a prefix for action types (e.g., 'auth/setCredentials')
  initialState,
  reducers: {
    // This reducer handles the 'auth/setCredentials' action
    setCredentials: (state, action) => {
      // Redux Toolkit uses Immer, so we can update state directly like this:
      state.user = action.payload; // The user data comes from the action's payload

      // We also store user info in localStorage for persistence
      localStorage.setItem("userInfo", JSON.stringify(action.payload));
    },
    // This reducer handles the 'auth/setAtlasAuth' action (for Atlas specific data)
    setAtlasAuth: (state, action) => {
      state.atlasAuth = action.payload;
      state.authProvider = "atlas";
      localStorage.setItem("atlasAuth", JSON.stringify(action.payload));
      localStorage.setItem("authProvider", "atlas");
    },
    // This reducer handles the 'auth/logout' action
    logout: (state) => {
      // Set state back to initial values
      state.user = null;
      state.atlasAuth = null;
      state.authProvider = "local";

      // Clear localStorage as well
      localStorage.removeItem("userInfo");
      localStorage.removeItem("atlasAuth");
      localStorage.removeItem("authProvider");
    },
    // This reducer handles toggling the sidebar state
    setOpenSidebar: (state, action) => {
      state.isSidebarOpen = action.payload; // payload is true or false
    },
  },
});

// createSlice automatically generates action creators from the reducer names
export const { setCredentials, setAtlasAuth, logout, setOpenSidebar } = authSlice.actions;

// The reducer function generated by createSlice
export default authSlice.reducer;
```
*   `createSlice` defines the `auth` slice of our state.
*   `initialState` sets the default values when the app starts. It checks `localStorage` so the user stays logged in even if they close and reopen the browser.
*   `reducers` contains functions (`setCredentials`, `logout`, etc.) that know how to update the state based on specific actions. Redux Toolkit lets us write these updates as if we were directly changing `state`, but it handles the complex immutable updates for us.
*   `authSlice.actions` gives us easy-to-use **action creators** (like `setCredentials()`) that create the action objects for us.
*   `authSlice.reducer` is the main reducer function for this slice that gets added to the store.

Now, let's see this slice in action during the login process in `Login.jsx`:

```jsx
// client\src\pages\Login.jsx (Simplified)
import { useForm } from "react-hook-form";
import { Button, Textbox } from "../components";
import { useProcessAtlasLoginMutation } from "../redux/slices/api/atlasServerApiSlice"; // RTK Query hook
import { useDispatch } from 'react-redux'; // Import useDispatch
import { setCredentials } from '../redux/slices/authSlice'; // Import our action creator

const Login = () => {
  const { register, handleSubmit } = useForm();
  const [processAtlasLogin, { isLoading }] = useProcessAtlasLoginMutation();
  const dispatch = useDispatch(); // Get the dispatch function

  const handleAtlasLogin = async (data) => {
    try {
      // Send login data to the backend (handled by RTK Query)
      const response = await processAtlasLogin({
        username: data.username,
        password: data.password,
      }).unwrap(); // .unwrap() gets the actual response data or throws an error

      console.log("Login successful!", response);

      // *** Dispatch the action to update Redux state ***
      dispatch(setCredentials(response)); // Call the action creator and dispatch the action object

      // ... further steps to navigate ...

    } catch (err) {
      console.error("Login failed:", err);
      // Show an error message...
    }
  };

  return (
    <div className="...">
      <form onSubmit={handleSubmit(handleAtlasLogin)}>
        <Textbox label="Username" {...register("username")} />
        <Textbox label="Password" type="password" {...register("password")} />
        <Button type="submit" label="Login with Atlas" isLoading={isLoading} />
      </form>
    </div>
  );
};

export default Login;
```
*   We import `useDispatch` from `react-redux` and our `setCredentials` action creator.
*   We call `useDispatch()` to get the `dispatch` function.
*   After the backend API call (using `useProcessAtlasLoginMutation` from [API Communication (RTK Query)](04_api_communication__rtk_query__.md)) is successful and we get the `response` (which contains user data), we call `dispatch(setCredentials(response))`.
*   This sends the `{ type: 'auth/setCredentials', payload: response }` action to the Redux store. The `authSlice` reducer sees this action and updates the `user` state in the store with the `response` data.

Now, any component can access this user data. For example, the `Profile.jsx` page from [Chapter 1](01_user_and_authentication_system_.md) uses `useSelector`:

```jsx
// client\src\pages\Profile.jsx (Simplified)
import React from 'react';
import { useSelector } from 'react-redux'; // Import useSelector
// ... other imports ...

const Profile = () => {
  // *** Use useSelector to read data from the Redux store ***
  const { user } = useSelector((state) => state.auth); // Access the 'auth' slice and get the 'user' property
  const userInfo = user?.user || user; // Access actual user details

  // ... rest of the component using userInfo?.name, userInfo?.email, etc. ...

  return (
    <PageContainer>
      {/* Display user info from the Redux state */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '24px', marginBottom: '24px' }}>
         <Avatar size={100} icon={<UserOutlined />} src={userInfo?.photoReference} />
         <Space direction="vertical">
            <Title level={3} style={{ margin: 0 }}>{userInfo?.name || 'Kullanıcı'}</Title>
            <Text type="secondary">{userInfo?.title || 'User'}</Text>
         </Space>
      </div>
      {/* ... more profile display code ... */}
    </PageContainer>
  );
};

export default Profile;
```
*   We import `useSelector` from `react-redux`.
*   We call `useSelector` with a small function `(state) => state.auth.user`. This function tells Redux, "Give me the `user` property from the `auth` slice of the overall state."
*   Redux automatically subscribes this component to changes in `state.auth.user`. If that state ever changes, the component will automatically re-render with the new data!

### Managing Task State with RTK Query (Built on Redux Toolkit)

For managing data fetched from APIs, Redux Toolkit provides a powerful add-on called **RTK Query**. RTK Query makes it incredibly simple to fetch, cache, and update data in your Redux store with very little code.

While we could manually dispatch actions (`fetchTasksStart`, `fetchTasksSuccess`, `fetchTasksError`) and write reducers to handle the task list state, RTK Query does all this boilerplate for us automatically!

From [Chapter 2](02_task_management_core_.md), we saw the `taskApiSlice.js` file:

```javascript
// client\src\redux\slices\api\taskApiSlice.js (Simplified)
import { TASKS_URL } from "../../../utils/contants";
import { apiSlice } from "../apiSlice"; // Our base API slice

// Inject endpoints specifically for tasks into the base API slice
export const postApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Define a query endpoint to get all tasks
    getAllTask: builder.query({
      query: ({
        strQuery, // Filter parameter for stage
        isTrashed, // Filter parameter for trash status
        search, // Search query
        // ... other filter parameters like priority, progressMin, etc.
      }) => {
        // Construct the URL with query parameters based on inputs
        let url = `${TASKS_URL}?stage=${strQuery}&isTrashed=${isTrashed}&search=${search}`;
        // Add other parameters if present...
        console.log('Task API query URL:', url);
        return { url, method: "GET", credentials: "include" };
      },
      // Tells RTK Query which cache tags this query provides (for invalidation)
      providesTags: ['Tasks'],
    }),

    // Define a mutation endpoint to create a task
    createTask: builder.mutation({
      query: (data) => ({
        url: `${TASKS_URL}/create`,
        method: "POST",
        body: data,
        credentials: "include",
      }),
      // Tells RTK Query to invalidate the 'Tasks' cache tag after a successful mutation
      invalidatesTags: ['Tasks'],
    }),

    // ... other mutation endpoints like updateTask, deleteTask, etc.
  }),
});

// RTK Query automatically generates hooks from your endpoint names
export const {
  useGetAllTaskQuery, // Hook for fetching tasks
  useCreateTaskMutation, // Hook for creating a task
  // ... hooks for other mutations and queries ...
} = postApiSlice;
```
*   This code defines how to communicate with the backend specifically for tasks.
*   `builder.query` defines a query endpoint (like `getAllTask`) for fetching data (`GET`). We define how to build the URL, including any parameters needed for filtering.
*   `builder.mutation` defines a mutation endpoint (like `createTask`) for changing data (`POST`, `PUT`, `DELETE`).
*   `providesTags` and `invalidatesTags` are used by RTK Query's caching system. When the `createTask` mutation successfully runs (which `invalidatesTags: ['Tasks']`), RTK Query knows that any query providing the `Tasks` tag (`useGetAllTaskQuery`) might be outdated and will automatically re-fetch the data! This keeps your task list updated automatically after you create, update, or delete a task.
*   RTK Query automatically generates custom React hooks (`useGetAllTaskQuery`, `useCreateTaskMutation`, etc.) that you can use directly in your components.

Now, let's see how `Tasks.jsx` from [Chapter 2](02_task_management_core_.md) uses the `useGetAllTaskQuery` hook:

```jsx
// client\src\pages\Tasks.jsx (Simplified)
import { useState } from "react";
import { Button, Loading, Tabs, Title } from "../components";
import { BoardView } from "../components/tasks";
// *** Import the generated RTK Query hook ***
import { useGetAllTaskQuery } from "../redux/slices/api/taskApiSlice";

const Tasks = () => {
  const [selected, setSelected] = useState(0);
  const [open, setOpen] = useState(false);

  // *** Use the hook to fetch tasks and manage state ***
  // The hook automatically handles fetching, loading, and storing data in Redux
  const { data, isLoading, error, refetch } = useGetAllTaskQuery({
      strQuery: 'todo', // Example: Fetch only 'todo' tasks initially
      isTrashed: false,
      search: '',
      // ... pass other filter states from component ...
  });

  // RTK Query stores the fetched data under data. We access the task list
  const tasks = data?.tasks || []; // Access the task list from the response structure

  // RTK Query provides isLoading and error states automatically
  if (isLoading) return <div className='py-10'><Loading /></div>;
  if (error) return <p>Error loading tasks: {error.message}</p>;

  return (
    <div>
      {/* Header */}
      <div className='flex items-center justify-between mb-6'>
        <Title title={'Tasks'} />
        <Button label={'Create Task'} onClick={() => setOpen(true)} />
      </div>

      {/* Tabs for different views */}
      <Tabs tabs={[]} setSelected={setSelected}>
        {/* Render the selected view based on 'selected' state */}
        {selected === 0 && (
          <div className="p-6">
            {/* Pass fetched tasks to BoardView component */}
            <BoardView tasks={tasks} onTaskUpdate={refetch} />
          </div>
        )}
        {/* ... other views ... */}
      </Tabs>

      {/* Add Task Modal */}
      <AddTask open={open} setOpen={setOpen} onSuccess={refetch} />
    </div>
  );
};

export default Tasks;
```
*   We call `useGetAllTaskQuery({})`. This hook automatically triggers the API request defined in `taskApiSlice.js`.
*   Crucially, this hook provides `data`, `isLoading`, and `error` properties. RTK Query manages the state for the fetched data, the loading status, and any errors *within the Redux store*.
*   When `isLoading` is true, we show a loading spinner.
*   When `data` is available, it contains the tasks fetched from the backend. We access this data and pass it down to components like `BoardView` for display.
*   The `refetch` function, also provided by the hook, allows us to manually trigger a re-fetch if needed (though `invalidatesTags` often handles this automatically).

### The Redux Store: Putting it All Together

The final piece is the central Redux store itself, defined in `store.js`:

```javascript
// client\src\redux\store.js
import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./slices/authSlice"; // Our auth slice reducer
import { apiSlice } from "./slices/apiSlice"; // Base RTK Query API slice
import { atlasApiSlice } from "./slices/api/atlasAuthApiSlice"; // Atlas auth RTK Query slice
import { atlasNotificationApiSlice } from "./slices/api/atlasNotificationApiSlice"; // Atlas notif RTK Query slice

// configureStore sets up the Redux store with good defaults
const store = configureStore({
  // The 'reducer' field tells Redux how to handle actions and update state
  reducer: {
    // Add reducers for each slice of state
    auth: authReducer, // The auth slice manages user login state

    // Add reducers for RTK Query API slices
    // RTK Query automatically creates a reducer for managing cached data and request status
    [apiSlice.reducerPath]: apiSlice.reducer, // Manages state for general API calls (like tasks)
    [atlasApiSlice.reducerPath]: atlasApiSlice.reducer, // Manages state for Atlas auth calls
    [atlasNotificationApiSlice.reducerPath]: atlasNotificationApiSlice.reducer, // Manages state for Atlas notif calls
  },
  // Middleware is code that runs between dispatching an action and the reducer
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      // We ignore certain actions for serialization checks (related to persistence)
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    })
      // Add the middleware for our RTK Query API slices
      // This middleware handles caching, automatic re-fetching, etc.
      .concat(apiSlice.middleware)
      .concat(atlasApiSlice.middleware)
      .concat(atlasNotificationApiSlice.middleware),

  // Enable Redux DevTools extension in non-production environments
  devTools: process.env.NODE_ENV !== 'production',
});

export default store;
```
*   `configureStore` is Redux Toolkit's function for setting up the store easily.
*   The `reducer` field is where we combine all our individual slice reducers. The keys (`auth`, `apiSlice.reducerPath`, etc.) define how the state is structured (e.g., `state.auth` will be managed by `authReducer`).
*   `apiSlice.reducerPath` is a unique key automatically generated by RTK Query for its reducer.
*   The `middleware` setup is important for RTK Query. It adds special middleware that handles the logic for fetching data, caching responses, tracking loading states, etc., automatically behind the scenes whenever you use the RTK Query hooks.
*   `devTools` makes it easy to inspect the Redux store and see every action and state change using the Redux DevTools browser extension, which is incredibly helpful for debugging!

### How It Works Together: A Simplified Flow

Let's trace the flow again with a simple sequence diagram:

```mermaid
sequenceDiagram
    participant Component as React Component
    participant useDispatch as useDispatch Hook
    participant ActionCreator as Action Creator (e.g., setCredentials)
    participant Dispatch as store.dispatch()
    participant ReduxMiddleware as Redux Middleware (incl. RTK Query)
    participant Reducer as Combined Reducer (incl. Slices)
    participant Store as Redux Store
    participant useSelector as useSelector Hook

    Component->>useDispatch: Call useDispatch()
    useDispatch-->>Component: Returns dispatch function
    Component->>ActionCreator: Call setCredentials(userData)
    ActionCreator-->>Component: Returns action object { type: 'auth/setCredentials', payload: userData }
    Component->>Dispatch: dispatch(action)
    Dispatch->>ReduxMiddleware: Action goes through middleware
    opt RTK Query Fetching
        ReduxMiddleware->>BackendAPI: Perform API Request
        BackendAPI-->>ReduxMiddleware: API Response (e.g., tasks data)
        ReduxMiddleware->>Store: Update RTK Query Cache & Loading State
    end
    ReduxMiddleware->>Reducer: Action reaches the reducer
    Reducer->>Store: Calculate new state based on action and old state
    Store-->>useSelector: Notify components listening for changes
    useSelector-->>Component: Provide updated state data
    Component->>Component: Re-renders with new data
```
1.  A **Component** wants to change or read data.
2.  To change state, it calls `useDispatch` to get the `dispatch` function, calls an **Action Creator** (like `setCredentials`) to create an **Action** object, and calls **`store.dispatch(action)`**.
3.  The action first goes through **Redux Middleware**. If it's an action related to RTK Query (e.g., a query hook is called, or a mutation finishes), the RTK Query middleware handles the API request, updates loading/error state, and manages the cache in the **Store**.
4.  After middleware, the action reaches the **Combined Reducer**. The reducer looks at the action's type and updates the relevant part of the state (managed by a specific slice reducer).
5.  The **Store** holds the complete, updated state.
6.  Components needing data use **`useSelector`** to read from the store. Redux notifies these components when the data they are interested in changes.
7.  The notified **Component** receives the new data from `useSelector` and **re-renders** to show the updated information.

This structured flow (Action -> Middleware -> Reducer -> Store Update -> Component Re-render) is the core principle of Redux and Redux Toolkit, ensuring that state changes are predictable and easy to trace.

### Conclusion

In this chapter, we introduced **Frontend State Management** and saw how **Redux Toolkit** provides a powerful and predictable way to manage your application's data in a central **Store**. We learned about the key concepts: State, Actions, Reducers, Dispatch, and Selectors, and how Redux Toolkit's `createSlice` and **RTK Query** simplify their implementation.

We saw concrete examples of how the `authSlice` manages user login state, allowing any component to access the logged-in user's information, and how RTK Query automatically handles fetching and caching task data, providing loading/error states and keeping your task lists updated.

Understanding how data is managed on the frontend is crucial for building reactive and maintainable user interfaces. Now that we know *where* the data lives and *how* it changes on the frontend, the next logical step is to dive deeper into how the frontend talks to the backend to get and send this data.

In the next chapter, we will explore **API Communication (RTK Query)** in more detail, specifically focusing on how `mytask` uses RTK Query to interact with the backend API endpoints we discussed in previous chapters.

[API Communication (RTK Query)](04_api_communication__rtk_query__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)