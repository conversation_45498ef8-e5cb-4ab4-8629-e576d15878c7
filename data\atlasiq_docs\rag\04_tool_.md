# Chapter 4: Tool

Welcome back! In the last chapter, [Query Router](03_query_router_.md), we learned how a [<PERSON><PERSON>](02_bot_.md) uses its Query Router to intelligently decide *if* and *which* specialized capabilities are needed to answer a user's question.

But what *are* these specialized capabilities? Where does the actual "doing" happen? That's where the **Tool** comes in!

## What Problem Does the Tool Solve?

Our goal is to build a system that can answer questions using information that isn't just locked inside its own brain. Questions might require:

*   Checking the latest news on the internet.
*   Looking up details in a customer database.
*   Finding specific paragraphs in a large collection of internal documents.

The core AI model ([Agent (LangGraphAgent)](05_agent__langgraphagent__.md)) is great at understanding language and generating text, but it doesn't have built-in, real-time access to the internet or your private databases.

The problem is: **How does the system connect to the outside world and get specific, up-to-date information when it needs it?**

This is the job of a **Tool**.

Think of **Tools** as specialized workers or utilities that the system can call upon. When the [Query Router](03_query_router_.md) decides that external information is needed, it knows which Tool to send out to get that information. Each Tool is designed to perform one specific task.

*   A "Web Search Tool" is like sending a researcher to Google.
*   A "Database Query Tool" is like asking a data analyst to run a SQL query.
*   A "Document Search Tool" is like having a librarian search through a specific collection of books.

Each Tool encapsulates the logic needed to interact with its specific external system.

## What Makes Up a Tool?

A Tool in this system is essentially a small piece of code designed to do one job: fetch information from somewhere outside the core AI.

Every Tool must:

1.  **Have a Specific Function:** Like searching, querying, or looking up.
2.  **Know How to Connect:** It needs configuration (like API keys, database connection strings, or file paths) to access the external service or data source.
3.  **Receive Instructions:** It needs a way to take the user's query (or a modified version of it) as input.
4.  **Provide Output:** It needs to return the information it found in a structured way that the rest of the system can understand.
5.  **Describe Itself:** It needs a description so the [Query Router](03_query_router_.md) (or an AI) knows what it does and when to use it.

## Where Do Tools Fit in the Flow?

We saw in the [Query Router](03_query_router_.md) chapter that the router decides *if* tools are needed and which ones. If tools are selected, the Router then *executes* them.

Here's the simplified flow again, highlighting the Tool's role:

```mermaid
sequenceDiagram
    participant AgenticRAG as AgenticRAG System
    participant BotC as Bot's Components
    participant QueryRouter as Query Router
    participant Tool as Tool
    participant ExternalService as External Service (Web, DB, etc.)

    AgenticRAG->>BotC: Get specific Bot's parts
    AgenticRAG->>QueryRouter: route_query(user_query, ...)
    QueryRouter->>QueryRouter: Decides which tools to use
    alt Tools Selected
        QueryRouter->>Tool: Execute selected Tool(s)
        Tool->>ExternalService: Request data
        ExternalService-->>Tool: Data/Results
        Tool-->>QueryRouter: Formatted Tool Results
    end
    QueryRouter-->>AgenticRAG: Tool Selection & Results
    AgenticRAG->>BotC: Pass results to Agent
    BotC-->>AgenticRAG: Final Response
```

The key part here is when the **Query Router** calls the **Tool**. The **Tool** then goes out and interacts with the **External Service** (like a web API or database) to get the actual data, formats it, and returns it to the **Query Router**. The Router then passes these results to the Bot's [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) to help formulate the final answer.

## How Are Tools Defined and Initialized?

Tools are defined in the Bot's configuration file ([Bot Configuration](07_bot_configuration_.md)). Remember the `tools` section in the `bot_config.yaml` example from Chapter 2?

```yaml
# Simplified snippet from configs/simple_bot.yaml
name: SimpleBot
# ... other parts ...

tools:
  - type: WebSearchTool # This is a Tool definition
    enabled: true
    config:
      max_results: 5
      # Add configuration like API keys etc. here

  - type: DocumentSearchTool # Another Tool definition
    enabled: false # This tool is not currently enabled for this bot
    config:
      collection_name: "internal_docs"
      # Add configuration like persistence path etc. here

# ... other parts ...
```

This configuration tells the system *which* Tools are available to a specific Bot and how each Tool should be set up.

When the [AgenticRAG System](01_agenticrag_system_.md) starts up and loads the Bot configurations, it calls a method called `_initialize_tools` for each Bot. This method reads the `tools` list from the config and creates an actual *instance* of each enabled Tool class.

```python
# File: app\core\agentic_rag.py (simplified _initialize_tools method)

    def _initialize_tools(self, bot_config: BotConfig) -> Dict[str, BaseTool]:
        """
        Initialize tools for a bot based on its configuration.
        """
        tools = {} # Dictionary to hold initialized tools

        # Loop through each tool defined in the bot's config
        for tool_config in bot_config.tools:
            if not tool_config.enabled: # Skip if the tool is disabled
                continue

            # Find the Python class for this tool type (e.g., WebSearchTool)
            tool_class = self.TOOL_CLASSES.get(tool_config.type)
            if not tool_class:
                logger.warning(f"Unknown tool type: {tool_config.type}")
                continue

            try:
                # Create an instance of the tool class, passing its specific config
                tool = tool_class(tool_config.config)
                tools[tool_config.type] = tool # Store the initialized tool
                logger.info(f"Initialized tool: {tool_config.type}")
            except Exception as e:
                logger.error(f"Error initializing tool {tool_config.type}: {str(e)}")

        return tools # Return the dictionary of initialized tools
```

**Explanation:**

*   The code iterates through the `tool_config` objects specified in the `bot_config`.
*   For each enabled tool, it looks up the corresponding Python class (like `WebSearchTool`, `DocumentSearchTool`, etc.) using a dictionary called `self.TOOL_CLASSES`.
*   It then creates a new instance of that Tool class, passing the `config` dictionary from the YAML file to the Tool's `__init__` method.
*   These initialized Tool instances are stored in a dictionary `tools`, keyed by the tool's `type` name (e.g., "WebSearchTool").
*   This `tools` dictionary is then passed to the [Query Router](03_query_router_.md) when it's initialized, so the router knows exactly which Tool *instances* it can use for this specific Bot.

## The Base Tool

All tools in this project follow a common structure defined by the `BaseTool` class. This ensures they can be easily managed and executed by the [Query Router](03_query_router_.md).

```python
# File: app\tools\base.py

from abc import ABC, abstractmethod
from typing import Any, Dict

class BaseTool(ABC):
    """Base class for all tools in the Agentic RAG system."""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the tool with configuration.
        """
        self.config = config # Store the config from the YAML
        self.name = self.__class__.__name__ # Get the class name as the tool name
        self.initialize() # Call the custom initialization method

    def initialize(self) -> None:
        """
        Initialize the tool with the provided configuration.
        Override this method to perform tool-specific initialization.
        """
        pass # Default implementation does nothing

    @abstractmethod # This indicates subclasses *must* implement this method
    async def execute(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Execute the tool with the given query.
        """
        pass # Must be implemented by subclasses

    @classmethod
    def get_tool_description(cls) -> str:
        """
        Get a description of what this tool does.
        """
        return cls.__doc__ or "No description available" # Uses the class docstring
```

**Explanation:**

*   `BaseTool(ABC)`: It inherits from `ABC` (Abstract Base Class), meaning it defines a structure that other classes *must* follow.
*   `__init__(self, config)`: The constructor receives the tool-specific `config` dictionary from the `bot_config.yaml` file and stores it. It also sets the tool's `name` automatically from the class name (e.g., `WebSearchTool`). Finally, it calls `self.initialize()`.
*   `initialize()`: This method is meant to be *overridden* by specific tool classes. This is where a tool would set up its connection to an external service (like creating a database client or initializing an API wrapper) using the provided `self.config`. The default implementation does nothing.
*   `execute(self, query, **kwargs)`: This is an `abstractmethod`, meaning any class inheriting from `BaseTool` *must* provide its own implementation of `execute`. This is the core method that the [Query Router](03_query_router_.md) will call. It takes the `query` (or instructions derived from it) and potentially other keyword arguments, interacts with the external service, and returns a dictionary containing the results. It's marked `async` because external calls often involve waiting.
*   `get_tool_description()`: This is a class method that provides a simple way for a tool to describe itself. By default, it uses the class's docstring (`"""Docstring here."""`). The [Query Router](03_query_router_.md) uses these descriptions to help the LLM decide which tools are relevant.

Any new Tool you want to add to the system would need to inherit from `BaseTool` and implement at least the `execute` method, and likely override `initialize` and provide a good docstring for `get_tool_description`.

## Examples of Specific Tools

Let's look briefly at what the `execute` method might do for a couple of example tools.

### WebSearchTool

```python
# File: app\tools\web_search.py (simplified execute method)

from app.tools.base import BaseTool
# Assume TavilyClient is imported and initialized in initialize()

class WebSearchTool(BaseTool):
    """Tool for searching the web using TavilySearch API."""
    
    async def execute(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search the web for information related to the query.
        """
        if not self.client: # Check if initialization was successful
            return {"success": False, "error": "Client not initialized", "results": []}
        
        try:
            # Use the initialized TavilyClient to perform the search
            response = self.client.search(
                query=query,
                max_results=kwargs.get("max_results", self.max_results),
                # ... other search parameters from config/kwargs ...
            )
            
            # Extract relevant results
            results = response.get("results", [])
            
            return {
                "success": True,
                "query": query,
                "results": results, # Return the found snippets/links
                "count": len(results)
            }
        except Exception as e:
            logger.error(f"Error executing web search: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    # ... get_tool_description() method ...
```

**Explanation:**

*   The `WebSearchTool` inherits from `BaseTool`.
*   Its `execute` method takes the `query`.
*   It uses the `self.client` (which was set up in `initialize` using the API key from the config) to call the external search API (TavilySearch).
*   It passes the query and potentially `max_results` (either from the config or overridden in `kwargs`).
*   It receives the response from the API, extracts the actual search `results`, and returns them in a dictionary. It also includes a `success` status and error information if something goes wrong.

### DocumentSearchTool

```python
# File: app\tools\document_search.py (simplified execute method)

from app.tools.base import BaseTool
# Assume vector_store is initialized in initialize()

class DocumentSearchTool(BaseTool):
    """Tool for searching documents using vector embeddings."""

    async def execute(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for documents relevant to the query.
        """
        if not self.vector_store: # Check if initialization was successful
            return {"success": False, "error": "Vector store not initialized", "documents": []}

        try:
            # Use the initialized vector store to perform a similarity search
            docs = self.vector_store.similarity_search(
                query,
                k=kwargs.get("top_k", self.top_k) # Number of documents to retrieve
            )

            # Format the retrieved documents
            results = []
            for doc in docs:
                results.append({"content": doc.page_content, "metadata": doc.metadata})

            return {"success": True, "documents": results, "count": len(results)}
        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")
            return {"success": False, "error": str(e), "documents": []}
    # ... get_tool_description() method ...
```

**Explanation:**

*   The `DocumentSearchTool` inherits from `BaseTool`.
*   Its `execute` method takes the `query`.
*   It uses the `self.vector_store` (which was set up in `initialize` using the collection name and persistence directory from the config) to perform a vector similarity search.
*   It retrieves the top `k` (number of) relevant documents.
*   It formats the retrieved document objects into a list of dictionaries containing the `content` and `metadata`.
*   It returns the formatted documents in a dictionary, along with success status and error info.

Other tools like `MongoDBQueryTool` and `SQLQueryTool` would have similar `execute` methods that use their initialized database clients (`self.db` or `self.engine`) to run queries, format the results (like rows from a table or documents from a collection), and return them. They also include LLM calls *within their `execute` logic* to convert natural language queries into the specific query language (SQL, MongoDB JSON) if needed, before actually executing the query.

This structured approach means that the [Query Router](03_query_router_.md) doesn't need to know the internal details of *how* a Web Search Tool works or *how* a Document Search Tool works. It just knows that it has a list of objects (the initialized Tools), and for each Tool, it can call its `execute` method with a query and expect a dictionary of results back.

## Conclusion

In this chapter, we focused on the **Tool** concept. We learned that Tools are the system's way of interacting with the external world – whether that's searching the web, querying a database, or searching documents. Each Tool is a specialized component designed for a single task, inheriting from a `BaseTool` class and implementing an `execute` method that performs the external interaction and returns results. Tools are configured per [Bot](02_bot_.md) and initialized by the [AgenticRAG System](01_agenticrag_system_.md). The [Query Router](03_query_router_.md) decides which Tools to use and calls their `execute` method to gather information needed to answer a query.

Now that we understand the different parts – the central system, the specialized bots, the router that decides what to do, and the tools that fetch information – let's look at the final piece of the puzzle: the component that uses all this information to generate the final answer. In the next chapter, we'll explore the **Agent**.

[Agent (LangGraphAgent)](05_agent__langgraphagent__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)