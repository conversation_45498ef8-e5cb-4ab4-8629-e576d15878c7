# Chapter 8: Internationalization (i18n)

Welcome back! In our journey through the `mytask` project, we've covered many essential parts: user accounts ([Chapter 1](01_user_and_authentication_system_.md)), tasks ([Chapter 2](02_task_management_core_.md)), how the frontend manages data ([Chapter 3](03_frontend_state_management__redux_toolkit__.md)) and talks to the backend ([Chapter 4](04_api_communication__rtk_query__.md)), how the backend handles requests ([Chapter 5](05_api_endpoints_and_controllers__backend_.md)), integrating with Atlas University ([Chapter 6](06_atlas_university_integration_.md)), and how data is structured in the database ([Chapter 7](07_database_models_.md)).

Now, let's think about our users. `mytask` is used by people from different backgrounds, potentially speaking different languages. If the application text is always in English, what about users who are more comfortable in Turkish? Or vice-versa?

This is where **Internationalization (i18n)** comes in.

### What is Internationalization (i18n)?

Imagine you're building a sign for a shop. If your sign is only in one language, only people who understand that language will know what the shop is. If you want to welcome more customers, you might add translations to your sign.

**Internationalization (i18n)** is the process of designing and developing your application so that it can be easily adapted (or "localized") to various languages and regions without engineering changes to the core code. The "i18n" is a common abbreviation because there are 18 letters between the 'i' and the 'n' in "internationalization".

In simpler terms, for `mytask`, i18n means setting up the app so that all the text you see on the screen – button labels, page titles, error messages, task descriptions – can be shown in either **Turkish** or **English**, based on the user's preference.

It makes the app accessible and friendly to a wider audience.

### Our Goal: Displaying Text in the Correct Language

Think about a button label, like the one on the login page. We've seen it display "Login with Atlas". But if the user prefers Turkish, we want it to display "Atlas ile Giriş".

The core use case for i18n is: **How does a frontend component know which language to use for a piece of text?**

### Key Concepts of i18n in `mytask`

`mytask` uses a common pattern for i18n on the frontend, based on React's Context API and simple JavaScript objects for translations.

Here are the key ideas:

1.  **Translation Dictionaries:** These are like **phrasebooks**. They are simple files (like `en.js` and `tr.js`) that contain all the text used in the application, organized by unique identifiers called **Keys**.
    *   Example: The key `login_with_atlas` maps to "Login with Atlas" in the English dictionary (`en.js`) and "Atlas ile Giriş" in the Turkish dictionary (`tr.js`).

2.  **Translation Function (`t`):** This is the **lookup tool**. Instead of writing the text directly in our components (e.g., `<Button label="Login with Atlas" />`), we call a special function, usually named `t` (short for "translate"). We give this function the **key** for the text we want (e.g., `t('login_with_atlas')`).
    *   The `t` function figures out the user's current language and looks up the key in the correct dictionary.

3.  **Language State & Context:** The application needs to **remember** which language the user has selected. This "current language" is stored in a state variable. React's Context API is used to make this current language state and the `t` function available to *any* component, anywhere in the application, without having to pass them down manually through props. Think of Context as a broadcast channel that components can tune into.

4.  **Language Switcher:** A UI element (like a button or dropdown) that allows the user to **change** the current language. When the language changes, the state is updated, the Context broadcasts the change, and all components using the `t` function automatically re-render with the text from the new language's dictionary.

### Solving the Use Case: Using Translations in Components

Let's see how a component uses the `t` function to display translated text.

We use a custom hook called `useLanguage` which gives us access to the current language and the `t` function.

Here's how the `Login.jsx` component uses it:

```jsx
// client\src\pages\Login.jsx (Simplified)
import { useForm } from "react-hook-form";
import { Button, Textbox } from "../components";
import { useProcessAtlasLoginMutation } from "../redux/slices/api/atlasServerApiSlice";
// *** Import the hook to access language features ***
import { useLanguage } from "../context/LanguageContext";

const Login = () => {
  const { register, handleSubmit } = useForm();
  const [processAtlasLogin, { isLoading }] = useProcessAtlasLoginMutation();
  // *** Get the translation function and current language from the hook ***
  const { t, language, changeLanguage } = useLanguage();

  // ... handleAtlasLogin function ...

  return (
    <div className="..."> {/* Layout */}
      <form onSubmit={handleSubmit(handleAtlasLogin)}>
        {/* Use t() to get translated labels */}
        <Textbox label={t("username")} {...register("username")} />
        <Textbox label={t("password")} type="password" {...register("password")} />
        {/* Use t() for the button label */}
        <Button type="submit" label={t("login_with_atlas")} isLoading={isLoading} />
      </form>
      {/* Example language switcher button */}
      <button onClick={() => changeLanguage(language === 'tr' ? 'en' : 'tr')}>
        Switch to {language === 'tr' ? 'English' : 'Turkish'}
      </button>
    </div>
  );
};

export default Login;
```
*   We import the `useLanguage` hook.
*   Calling `const { t, language, changeLanguage } = useLanguage();` gives us the tools: the `t` function, the `language` variable (e.g., 'en' or 'tr'), and the `changeLanguage` function.
*   Now, instead of fixed text like `"Username"`, we use `t("username")`. The `t` function will look up the key `"username"` in the correct dictionary based on the current `language` state and return "Username" or "Kullanıcı Adı".
*   The `changeLanguage` function can be hooked up to a button or dropdown, allowing the user to switch languages. When called, it updates the `language` state in the Context, triggering a re-render of all components using `useLanguage` with the new translations.

Similarly, in the `Tasks.jsx` page:

```jsx
// client\src\pages\Tasks.jsx (Simplified)
import { useState } from "react";
import { Button, Loading, Tabs, Title } from "../components";
import { BoardView } from "../components/tasks";
import { useGetAllTaskQuery } from "../redux/slices/api/taskApiSlice";
// *** Import the hook to access language features ***
import { useLanguage } from "../context/LanguageContext";

const Tasks = () => {
  const { data, isLoading, refetch } = useGetAllTaskQuery({});
  const [selected, setSelected] = useState(0);
  const [open, setOpen] = useState(false);
  // *** Get the translation function ***
  const { t } = useLanguage();

  const tasks = data?.tasks || [];

  // Translate tab titles using t()
  const TABS = [
    { title: t("boardView"), icon: null },
    { title: t("listView"), icon: null },
    { title: t("treeView"), icon: null },
  ];

  if (isLoading) return <div className='py-10'><Loading /></div>;

  return (
    <div>
      <div className='flex items-center justify-between mb-6'>
        {/* Translate page title */}
        <Title title={t('tasks')} />
        {/* Translate button label */}
        <Button label={t('createTask')} onClick={() => setOpen(true)} />
      </div>

      {/* Use the translated TABS array */}
      <Tabs tabs={TABS} setSelected={setSelected}>
        {/* ... render view based on selected ... */}
      </Tabs>

      {/* Add Task Modal */}
      <AddTask open={open} setOpen={setOpen} onSuccess={refetch} />
    </div>
  );
};

export default Tasks;
```
*   Again, we get the `t` function using `useLanguage()`.
*   We use `t()` to translate the main page title, the "Create Task" button label, and the titles for the different task views within the `TABS` array.

This pattern is used throughout the application to translate all user-facing text.

### Behind the Scenes: The Language Context and Translations

The magic happens in the `LanguageContext.jsx` file and the translation dictionary files (`en.js`, `tr.js`).

Let's look at a simplified `LanguageContext.jsx`:

```javascript
// client\src\context\LanguageContext.jsx (Simplified)
import React, { createContext, useState, useContext, useEffect } from 'react';
// *** Import the translation dictionaries ***
import translations from '../translations'; // Imports the object containing { en, tr }

// Default language if none is saved or detected
const defaultLanguage = 'tr';

// *** 1. Create a Context ***
const LanguageContext = createContext();

// *** 2. Create a Provider Component ***
export const LanguageProvider = ({ children }) => {
  // *** 3. Manage the current language state ***
  const [language, setLanguage] = useState(() => {
    // Initialize state by checking localStorage for a saved preference
    const savedLanguage = localStorage.getItem('language');
    return savedLanguage || defaultLanguage; // Use saved or default
  });

  // *** Side effect: Save language to localStorage when it changes ***
  useEffect(() => {
    localStorage.setItem('language', language);
    // We might also update libraries like moment.js here for date/time formatting
    // moment.locale(language === 'tr' ? 'tr' : 'en-gb');
  }, [language]); // Re-run this effect whenever 'language' state changes

  // *** Function to change the language state ***
  const changeLanguage = (lang) => {
    setLanguage(lang); // Update the state
  };

  // *** 4. The Translation Function (the 't' function) ***
  const t = (key, params) => {
    // Look up the key in the currently selected language's dictionary
    const translation = translations[language]?.[key] // Try current language
                       || translations.en[key]      // Fallback to English if key missing in current lang
                       || key;                     // Fallback to the key itself if not found anywhere

    // Handle cases where translation is a function (e.g., for pluralization or dynamic text)
    if (typeof translation === 'function' && params) {
      return translation(params); // Call the function with provided parameters
    }

    return translation; // Return the found translation string
  };

  // *** 5. Value provided by the Context ***
  const value = {
    language,         // Current language state
    changeLanguage,   // Function to change language
    t,                // The translation lookup function
    availableLanguages: Object.keys(translations) // List of available languages
  };

  // Provide the value to all children components wrapped by this Provider
  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// *** 6. Custom Hook to consume the Context ***
export const useLanguage = () => {
  // useContext makes the 'value' from the nearest Provider available
  const context = useContext(LanguageContext);
  if (context === undefined) {
    // Ensure the hook is used inside the Provider
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context; // Return the value object { language, changeLanguage, t, ... }
};

// We export the provider and the hook
export default LanguageContext;
```
*   `createContext()` creates the actual context object that components can subscribe to.
*   `LanguageProvider` is a standard React component that wraps part of your application tree. It uses `useState` to manage the `language` value. The initial language is loaded from `localStorage` so the user's preference persists across visits.
*   An `useEffect` hook saves the current `language` to `localStorage` whenever it changes.
*   The `t` function is defined within the provider. It takes a `key` and optionally `params`. It looks up the `key` first in `translations[language]`, then falls back to `translations.en`, and finally just returns the `key` itself if no translation is found. This provides robustness. It also handles translations that are functions (useful for things like "You have {count} new tasks", where the text might change based on the number).
*   The `value` object bundles the `language` state, `changeLanguage` function, and `t` function. This `value` is passed to the `LanguageContext.Provider`.
*   Any component rendered *inside* the `LanguageProvider` can use the `useContext(LanguageContext)` hook (or our custom `useLanguage` wrapper hook) to get this `value` object and access the `t` function and `language` state.
*   The `useLanguage` hook is just a convenience wrapper around `useContext` that also adds a helpful error check.

The `translations` object is imported from a file that gathers the separate language files:

```javascript
// client\src\translations\index.js (Example structure - actual file not provided in snippets)
import en from './en';
import tr from './tr';

// Combine imported language dictionaries into a single object
const translations = {
    en, // English translations
    tr  // Turkish translations
};

export default translations; // Export the combined object
```

And the individual language dictionary files (`en.js`, `tr.js`) look like this (simplified snippets from the provided code):

```javascript
// client\src\translations\en.js (Simplified)
// English translations
const en = {
  // Common
  app_name: "Istanbul Atlas University",
  loading: "Loading...",
  save: "Save",
  cancel: "Cancel",
  delete: "Delete",
  actions: "Actions",

  // Auth
  login: "Login",
  password: "Password",
  login_with_atlas: "Login with Atlas",
  username: "Username", // Key for username
  login_successful: "Login successful!",

  // Tasks
  tasks: "All Tasks", // Key for "All Tasks" page title
  createTask: "Create Task", // Key for the button label
  boardView: "Board View",
  listView: "List View",
  treeView: "Tree View",

  // ... many more keys and translations ...

  // Example of a dynamic translation (uses a parameter)
  team_members_selected: function(params) {
    const { count } = params;
    // Basic pluralization example
    return `${count} team member${count === 1 ? '' : 's'} selected.`;
  },

   // Example of a date format function translation (takes params)
  date_format: function(params) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return params.date.toLocaleDateString('en-US', options);
  },
};

export default en;
```

```javascript
// client\src\translations\tr.js (Simplified)
// Turkish translations
const tr = {
  // Common
  app_name: "İstanbul Atlas Üniversitesi",
  loading: "Yükleniyor...",
  save: "Kaydet",
  cancel: "İptal",
  delete: "Sil",
  actions: "İşlemler",

  // Auth
  login: "Giriş",
  password: "Şifre",
  login_with_atlas: "Atlas ile Giriş",
  username: "Kullanıcı Adı", // Corresponding Turkish translation for username key
  login_successful: "Giriş başarılı!",

  // Tasks
  tasks: "Tüm Görevler", // Corresponding Turkish translation
  createTask: "Görev Oluştur", // Corresponding Turkish translation
  boardView: "Pano Görünümü",
  listView: "Liste Görünümü",
  treeView: "Ağaç Görünümü",

  // ... many more keys and translations ...

  // Example of a dynamic translation (uses a parameter)
  team_members_selected: function(params) {
    const { count } = params;
    // Turkish doesn't always need plural suffix change based on count = 1
    return `${count} ekip üyesi seçildi.`;
  },

  // Example of a date format function translation (takes params)
   date_format: function(params) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return params.date.toLocaleDateString('tr-TR', options);
  },
};

export default tr;
```
*   These files are simple JavaScript objects where keys (like `username`, `login_with_atlas`) map to the translated strings in that language.
*   Notice that some translations are functions. This is a powerful technique used when the text needs to include dynamic values (like a count) or perform formatting (like dates). The `t` function in `LanguageContext.jsx` is designed to call these functions if the translation value is a function and parameters are provided.

To make this system work for the entire application, the `LanguageProvider` component needs to wrap the root of your React application, usually in `App.jsx` or `index.js`.

```jsx
// client\src\App.jsx (Simplified Wrapper)
import React from 'react';
import { LanguageProvider } from './context/LanguageContext'; // Import the Provider
// ... other imports like Router, Redux Provider ...

function App() {
  return (
    // Wrap the entire app with the LanguageProvider
    <LanguageProvider>
      {/* ... Redux Provider, Router, and other app components ... */}
      {/* All components inside can now use useLanguage() */}
    </LanguageProvider>
  );
}

export default App;
```
*   By wrapping the main application components with `<LanguageProvider>`, all components within the tree gain access to the language context.

### Server-Side Translation (Briefly)

While most of the user-facing text is handled on the frontend, sometimes the backend needs to send messages (like error messages or email content for notifications) that are also translated.

The `mytask` backend has a similar, simpler system using a `languageMiddleware.js` to detect the user's preferred language from the request (often via a cookie or header set by the frontend) and server-side translation dictionaries (`server\utils\translations.js`) and a `getTranslation` function.

```javascript
// server\middleware\languageMiddleware.js (Simplified)
import { defaultLanguage } from "../utils/translations.js";

export const languageMiddleware = (req, res, next) => {
  let language = req.cookies.language // Check cookie set by frontend
                || req.headers["accept-language"]?.split(',')[0].trim().split('-')[0] // Check header
                || req.query.lang // Check query parameter
                || defaultLanguage; // Fallback to default

  // Validate against supported languages
  if (!['tr', 'en'].includes(language)) {
      language = defaultLanguage;
  }

  req.language = language; // Attach language to request object
  next();
};
```

```javascript
// server\utils\translations.js (Simplified)
export const translations = {
  tr: {
    notFound: "Bulunamadı",
    notAuthorized: "Yetkili değilsiniz",
    internalError: "Bir hata oluştu",
    newTaskAssigned: "Size yeni bir görev atandı"
  },
  en: {
    notFound: "Not found",
    notAuthorized: "Not authorized",
    internalError: "An error occurred",
    newTaskAssigned: "A new task has been assigned to you"
  }
};

export const defaultLanguage = 'tr';

// Server-side translation lookup function
export const getTranslation = (key, language = defaultLanguage) => {
  return translations[language]?.[key] || translations['en'][key] || key;
};
```
*   The `languageMiddleware` runs before controller functions ([Chapter 5](05_api_endpoints_and_controllers__backend_.md)) and attaches the detected language to `req.language`.
*   Backend code (controllers, utilities) can then use the `getTranslation(key, req.language)` function to get translated messages. This ensures backend-originated messages match the user's selected language as well.

### i18n Flow Visualization (Frontend)

Here's a simple diagram showing how a component gets translated text on the frontend:

```mermaid
sequenceDiagram
    participant Component as React Component
    participant useLanguage as useLanguage Hook
    participant LanguageContext as Language Context
    participant LanguageProvider as LanguageProvider Component
    participant Translations as Translation Dictionaries<br>(en.js, tr.js)

    Component->>useLanguage: Call useLanguage()
    useLanguage->>LanguageContext: Access Context value
    LanguageContext-->>useLanguage: Return { language, t, ... }
    useLanguage-->>Component: Return { language, t, ... }

    Component->>Component: Render UI using t('some_key')
    Component->>t: Call t('some_key')
    t->>LanguageProvider: Get current language state
    t->>Translations: Lookup 'some_key' in language dictionary
    Translations-->>t: Return translated string
    t-->>Component: Return translated string
    Component->>Component: Display translated string
```

This diagram shows how the component obtains the `t` function and the language state via the hook and context, and then uses the `t` function to retrieve the appropriate text from the translation dictionaries based on the current language.

### Conclusion

In this chapter, we explored **Internationalization (i18n)**, understanding how the `mytask` application displays text in multiple languages, primarily Turkish and English. We learned about the key concepts: **Translation Dictionaries** storing text by **Keys**, the **Translation Function (`t`)** for looking up text, and using React's **Context** (`LanguageContext`, `LanguageProvider`, `useLanguage`) to manage the current language state and make the `t` function available throughout the frontend. We also briefly touched upon how the backend handles translations for server-originated messages.

By using i18n, `mytask` becomes much more usable for people preferring different languages, making it truly international-ready.

This concludes our initial deep dive into the core components and systems of the `mytask` project. We've covered everything from user authentication and task management to frontend state, API communication, backend structure, external integrations, database models, and internationalization.

You now have a foundational understanding of the major pieces that make up the `mytask` application!

---
*This is the final chapter of this tutorial.*

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)