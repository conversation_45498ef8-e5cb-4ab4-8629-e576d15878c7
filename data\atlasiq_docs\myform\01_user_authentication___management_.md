# Chapter 1: User Authentication & Management

Welcome to the first chapter of the Myform tutorial! We're going to start with a fundamental concept that's crucial for almost any application: **User Authentication & Management**.

Think of our Myform application like a special building or club at Atlas University. Not everyone can just walk in and access everything. We need a **security system** and a **membership list**.

The "User Authentication & Management" part of Myform is exactly this system!

*   **Authentication:** It checks *who you are*. Are you a student or staff member at Atlas University? Can you prove it with your Atlas credentials (like your username and password)?
*   **Management:** It keeps track of the *membership list*. What's your name? What's your role (are you just a regular member, or are you an "admin" who can manage other members)? It also makes sure only members (and sometimes specific types of members like admins) can access certain areas or features of the building.

In short, this is the system that ensures only the right people can use Myform and that they can only do what they're allowed to do.

Our main goal in this chapter is to understand how a user logs into Myform using their Atlas University credentials and how the application remembers who they are.

## How Users Log In

Let's walk through the most common use case: **A user needs to log in to access their forms.**

When you first visit Myform, if you're not logged in, you'll see a login page. This page asks for your Atlas University username and password.

Here's a look at the login page's code (`app/login/page.tsx`):

```typescript
import { Suspense } from "react";
import LoginForm from "@/components/auth/LoginForm";

function LoginFormWrapper() {
  return <LoginForm />;
}

export default function LoginPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      {/* This shows the actual login form component */}
      <Suspense fallback={<div>Loading...</div>}>
        <LoginFormWrapper />
      </Suspense>
    </div>
  );
}
```

This code is simple; it just sets up the layout and then displays our actual login form component, called `LoginForm`.

Now, let's look at the `LoginForm` component (`components/auth/LoginForm.tsx`):

```typescript
// Inside components/auth/LoginForm.tsx
"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext"; // <--- This is key!

interface LoginFormData {
  username: string;
  password: string;
}

export default function LoginForm() {
  // Get the login function and user state from our AuthContext
  const { login, isAuthenticated } = useAuth();
  const router = useRouter();
  // ... other state and hooks

  const { handleSubmit, register } = useForm<LoginFormData>();

  // ... useEffect to redirect if already authenticated

  const onSubmit = async (data: LoginFormData) => {
    // ... loading state logic
    try {
      console.log('Submitting login form...');
      // Call the login function provided by the AuthContext
      const success = await login(data.username, data.password);

      if (success) {
        toast.success("Login successful!"); // Show success message
        // Redirect the user after successful login
        const redirectPath = searchParams?.get('redirect') || '/dashboard';
        window.location.href = targetPath; // Using window.location.href for hard refresh
      } else {
        toast.error("Login failed. Please check your credentials."); // Show error message
        // ... show error modal
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error("An unexpected error occurred."); // Show generic error
      // ... show error modal
    } finally {
      // ... turn off loading state
    }
  };

  return (
    // ... form UI using handleSubmit and register
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Username input */}
      <div>
        <label htmlFor="username" /* ... */>{/* Label text */}</label>
        <input id="username" type="text" {...register("username", { required: true })} /* ... */ />
        {/* Error message */}
      </div>
      {/* Password input */}
      <div>
        <label htmlFor="password" /* ... */>{/* Label text */}</label>
        <input id="password" type="password" {...register("password", { required: true })} /* ... */ />
        {/* Show/hide password button */}
        {/* Error message */}
      </div>
      {/* Language switcher */}
      {/* Submit button */}
      <div>
        <button type="submit" disabled={isLoading}>
          {isLoading ? 'Logging in...' : 'Sign In'}
        </button>
      </div>
    </form>
    // ... modals and other UI
  );
}
```

This form uses the `react-hook-form` library to handle collecting the username and password. When you click the "Sign In" button, the `onSubmit` function runs. Inside `onSubmit`, we call a special `login` function that comes from something called `useAuth()`.

What is `useAuth()`? It's a **Context** ([Contexts explained in a future chapter](link/to/contexts/chapter.md)) that helps us manage the user's authentication state throughout the application. Think of it as a central place that knows if you're logged in, who you are, and provides functions like `login` and `logout`.

Here's a simplified look at the `AuthContext` (`contexts/AuthContext.tsx`):

```typescript
// Inside contexts/AuthContext.tsx
"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define what a logged-in user object looks like
interface AuthUser {
  _id: string; // Our database ID
  email: string;
  firstName: string;
  lastName: string;
  role: string; // 'user' or 'admin'
  isAdmin: boolean;
  // ... other user properties
  token?: string; // Our application's token
  atlasToken?: string; // Token received from Atlas API
}

interface AuthContextType {
  user: AuthUser | null; // The logged-in user object, or null
  isLoading: boolean; // Is the auth process currently loading?
  isAuthenticated: boolean; // Are they logged in?
  error: string | null; // Any authentication error message
  login: (username: string, password: string) => Promise<boolean>; // The login function
  logout: () => void; // The logout function
  // ... other helper functions
}

// Create the context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  // State to hold the current user
  const [user, setUser] = useState<AuthUser | null>(null);
  // State to track loading status
  const [isLoading, setIsLoading] = useState(true);
  // State to track errors
  const [error, setError] = useState<string | null>(null);
  // State to know if the initial auth check is done
  const [authChecked, setAuthChecked] = useState(false);

  // Effect to load user from browser storage when the app starts
  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);
        // Basic check if parsed user is valid
        if (parsedUser && parsedUser.email) {
           setUser(parsedUser);
           // Restore cookies if token is present (important for server components)
           if (parsedUser.token) {
             document.cookie = `token=${parsedUser.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Strict`;
             document.cookie = `userEmail=${parsedUser.email}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Strict`;
           }
        } else {
           localStorage.removeItem('user'); // Clear invalid data
        }
      } catch (e) {
        console.error("Failed to parse user from localStorage", e);
        localStorage.removeItem('user');
      }
    }
    setIsLoading(false);
    setAuthChecked(true); // Mark auth check as done
  }, []);

  // The actual login function
  const login = async (username: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null); // Clear previous errors
    try {
      // This is where the magic happens: Call our backend API route!
      const response = await fetch('/api/atlas-auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        // If the response status is not OK (e.g., 401, 500), throw an error
        throw new Error(data.message || 'Login failed');
      }

      // If login is successful, update state and storage
      localStorage.setItem('user', JSON.stringify(data)); // Save user data to browser
      setUser(data); // Set user state

      // Also set cookies (especially token and email) for server-side access
      if (data.token) {
        document.cookie = `token=${data.token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Strict`;
      }
      if (data.email) {
        document.cookie = `userEmail=${data.email}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Strict`;
      }


      return true; // Indicate success
    } catch (error: any) {
      setError(error.message); // Store the error message
      return false; // Indicate failure
    } finally {
      setIsLoading(false); // Turn off loading
    }
  };

  const logout = async () => {
    // Clear server tokens via API call
    await fetch('/api/auth/clear-tokens', { method: 'POST' });
    // Clear local storage and cookies
    localStorage.removeItem('user');
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    document.cookie = 'userEmail=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    // Clear state
    setUser(null);
    setError(null);
    // Redirect to login page (hard refresh to ensure state is reset)
    window.location.href = '/login';
  };

  // Provide the state and functions to the components
  return (
    <AuthContext.Provider
      value={{ user, isLoading, isAuthenticated: !!user, error, login, logout, checkAuthReady: () => authChecked }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Hook to easily use the AuthContext
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
```

The `useAuth` hook provides the `login` function. This `login` function is responsible for talking to the backend to actually perform the authentication. Notice that it calls `/api/atlas-auth/login`. This is our custom API route where the real work of talking to Atlas happens.

The `AuthProvider` also uses `localStorage` and cookies to remember the user's information across page loads and even browser restarts. This is why you don't have to log in every single time you open the application.

## Under the Hood: The Login Process

So, what happens when the `login` function in our `AuthContext` calls `/api/atlas-auth/login`? Let's look at the API route file (`app/api/atlas-auth/login/route.ts`).

Here's a simplified step-by-step sequence:

```mermaid
sequenceDiagram
    participant User as User (Browser)
    participant LoginForm as LoginForm (Client Component)
    participant AuthContext as AuthContext (Client Context)
    participant OurBackendAPI as Our Backend API (/api/atlas-auth/login)
    participant AtlasAPI as Atlas University API
    participant OurDatabase as Our Database

    User->>LoginForm: Enters username/password
    LoginForm->>AuthContext: Calls login(username, password)
    AuthContext->>OurBackendAPI: Makes POST request to /api/atlas-auth/login
    OurBackendAPI->>AtlasAPI: Sends username/password to Atlas Login endpoint
    AtlasAPI-->>OurBackendAPI: Returns success/failure + Atlas Token
    alt If Atlas Login Successful
        OurBackendAPI->>AtlasAPI: Uses Atlas Token to get user details
        AtlasAPI-->>OurBackendAPI: Returns user details (name, email, etc.)
        OurBackendAPI->>OurDatabase: Finds or Creates user based on Atlas data
        OurDatabase-->>OurBackendAPI: Returns user data from our DB
        OurBackendAPI->>OurBackendAPI: Creates our own JWT Token
        OurBackendAPI-->>AuthContext: Sends our JWT Token + User Data + Sets Cookies
        AuthContext->>LoginForm: Updates user state
        LoginForm->>User: Redirects to dashboard
    else If Atlas Login Failed
        OurBackendAPI-->>AuthContext: Sends Login Failed message
        AuthContext->>LoginForm: Updates error state
        LoginForm->>User: Shows error message
    end
```

Let's break down the key parts of the API route code (`app/api/atlas-auth/login/route.ts`):

```typescript
// Inside app/api/atlas-auth/login/route.ts
import { NextResponse } from "next/server";
import axios from "axios"; // Used to make requests to the Atlas API
import jwt from "jsonwebtoken"; // Used to create our application's token
import connectDB from "@/lib/mongodb"; // Connect to our database
import User from "@/models/User"; // Our User model

export async function POST(req: Request) {
  try {
    // 1. Get username and password from the request body
    const { username, password } = await req.json();
    console.log('Login attempt for user:', username); // Log for debugging

    // Get Atlas API URL and our JWT secret from environment variables
    const apiBaseUrl = process.env.ATLAS_API_BASE_URL || 'https://myapi2.atlas.edu.tr/api';
    const jwtSecret = process.env.JWT_SECRET || 'your_jwt_secret_here';


    // 2. Call the Atlas API to authenticate the user
    console.log('Making Atlas API request...');
    const loginResponse = await axios.post(
      `${apiBaseUrl}/Login/Login`,
      { username, password, rememberMe: false, applicationName: "MYATLAS", isMobile: false }
    );

    // 3. Check the response from Atlas
    if (!loginResponse.data.webToken || loginResponse.data.isLogin !== true) {
      console.error('Atlas authentication failed');
      // If Atlas says no, return an error to the client
      return NextResponse.json({ message: "Invalid credentials" }, { status: 401 });
    }

    const atlasToken = loginResponse.data.webToken;
    console.log('Atlas authentication successful, token received.');

    // 4. Get user data from Atlas using the token they gave us
    console.log('Fetching user data from Atlas API...');
    const userDataResponse = await axios.get(
      `${apiBaseUrl}/Login/LoginData`,
      { headers: { Authorization: `Bearer ${atlasToken}` } }
    );
    const atlasUserData = userDataResponse.data;

    // 5. Connect to our database ([Database Connection & Models](06_database_connection___models_.md))
    await connectDB();
    console.log('Connected to database.');

    // 6. Find or create the user in *our* database using Atlas data
    const atlasUserId = atlasUserData.userId || atlasUserData.id;
    const email = atlasUserData.email || username;

    // Try to find user by Atlas ID or email
    let user = await User.findOne({ atlasUserId }) || await User.findOne({ email });
    console.log('Database user search result:', user ? `Found user ${user._id}` : 'User not found');

    // ... logic to extract firstName, lastName, etc. from Atlas data ...

    if (user) {
      // If user exists, update their details (like name, role, last login)
      console.log('Updating existing user:', user._id);
      // ... update user fields from atlasUserData ...
      user.lastLoginAt = new Date();
      user.atlasToken = atlasToken; // Store Atlas token for future API calls (e.g., getting profile details)
      await user.save(); // Save changes
    } else {
      // If user doesn't exist, create a new one in our DB
      console.log('Creating new user...');
      const newUser = await User.create({
        email,
        firstName: "...", // Extracted first name
        lastName: "...",  // Extracted last name
        role: atlasUserData.role === 'admin' ? 'admin' : 'user', // Assign role based on Atlas data
        atlasUserId,
        atlasToken, // Store Atlas token
        isExternal: true, // Mark as a user authenticated via external system (Atlas)
        lastLoginAt: new Date(),
        status: 'active',
        isActive: true,
        settings: new Map(),
      });
      user = newUser;
      console.log('New user created:', user._id);
    }

    // 7. Create *our* application's JWT token for the user
    const token = jwt.sign(
      { userId: user._id, email: user.email, role: user.role, atlasUserId: user.atlasUserId },
      jwtSecret,
      { expiresIn: '7d' } // Token expires in 7 days
    );
    console.log('JWT token created.');

    // 8. Prepare user data to send back to the client
    const userData = {
      _id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isAdmin: user.role === 'admin',
      profileImage: user.profileImage,
      token, // Include our token
      atlasToken // Include Atlas token (optional, depends on needs)
    };

    // 9. Send the response back to the client and set cookies
    const response = NextResponse.json(userData);
    response.cookies.set('token', token, {
      httpOnly: true, // Cannot be accessed by client-side JavaScript
      secure: process.env.NODE_ENV === 'production', // Only send over HTTPS in production
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // Same expiration as the token
      path: '/' // Available application-wide
    });
     response.cookies.set('userEmail', user.email, { // Set email cookie as backup
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60,
      path: '/'
    });
    console.log('Response sent with cookies.');

    return response;

  } catch (error: any) {
    console.error('Login route error:', error.message);
    if (axios.isAxiosError(error) && error.response) {
        console.error('Atlas API Response status:', error.response.status);
        console.error('Atlas API Response data:', error.response.data);
         // Return a more specific error if it came from Atlas
        if (error.response.status === 401) {
             return NextResponse.json(
                { message: "Invalid credentials (from Atlas)" },
                { status: 401 }
            );
        }
    }
    // Handle other errors
    return NextResponse.json(
      { message: "Authentication failed", error: error.message },
      { status: 500 }
    );
  }
}
```

This API route does several important things:

1.  It receives the username and password from the client.
2.  It makes a request to the official Atlas University API (`https://myapi2.atlas.edu.tr/api/Login/Login`) to verify the credentials.
3.  If the Atlas API confirms the login, it usually sends back a special `webToken` (an Atlas token).
4.  Our API then uses this Atlas token to fetch more detailed user information from another Atlas API endpoint (`/Login/LoginData`).
5.  It connects to *our own* MongoDB database ([Database Connection & Models](06_database_connection___models_.md)).
6.  It checks if a user with this Atlas ID or email already exists in our database.
7.  If the user exists, it updates their information (like name, role, last login time) based on the latest data from Atlas.
8.  If the user doesn't exist, it creates a *new* user record in our database using the information from Atlas. This syncs Atlas users into our local system. The details of our `User` model are defined in `models/User.ts`.
9.  It creates a **JSON Web Token (JWT)** specifically for *our* application. This token contains basic user info (like their ID and role in our system) and is signed with a secret key so we can verify its authenticity later.
10. It sends the user's information (including our JWT token) back to the client. It also sets our JWT token (and user email as a backup) as secure HTTP-only cookies in the user's browser. Cookies are the primary way the browser remembers you for server-side operations.

The `User` model mentioned above (`models/User.ts`) defines the structure of how we store user information in our database:

```typescript
// Inside models/User.ts
import mongoose, { Schema, model, models } from 'mongoose';

export interface IUser {
  email: string; // User's email (unique)
  firstName: string;
  lastName: string;
  nameSurname?: string; // Optional: Full name from Atlas if available
  photoName?: string; // Optional: Profile picture URL from Atlas if available
  role: 'user' | 'admin'; // User's role in *our* system
  status: 'active' | 'suspended'; // User's status in *our* system
  profileImage?: string; // Combined profile image field
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  isActive: boolean; // Is the account active?
  settings: Map<string, any>; // User-specific settings

  // Fields specific to Atlas login
  atlasUserId?: string; // Unique ID from the Atlas system
  atlasToken?: string; // Optional: Store Atlas token (marked as select: false for security)
  isExternal: boolean; // True if authenticated via an external system (like Atlas)
  title?: string; // Optional: Title from Atlas
  password?: string; // Optional: Password for manually created users (not used for Atlas users, marked select: false)
}

const userSchema = new Schema<IUser>({
  email: { type: String, required: true, unique: true },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  nameSurname: { type: String }, // Store Atlas nameSurname
  photoName: { type: String }, // Store Atlas photoName
  role: { type: String, enum: ['user', 'admin'], default: 'user' },
  status: { type: String, enum: ['active', 'suspended'], default: 'active' },
  profileImage: { type: String }, // Store profile image URL
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  lastLoginAt: { type: Date },
  isActive: { type: Boolean, default: true },
  settings: { type: Map, of: Schema.Types.Mixed, default: {} },
  atlasUserId: { type: String, unique: true, sparse: true }, // Use sparse index for optional unique fields
  atlasToken: { type: String, select: false }, // Don't fetch by default
  isExternal: { type: Boolean, default: false },
  title: { type: String },
  password: { type: String, select: false }, // Don't fetch by default
});

// Automatically update the 'updatedAt' field on save
userSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Create the Mongoose model
const User = models.User || model('User', userSchema);

export default User;
```

This schema includes fields for basic user information (`email`, `firstName`, `lastName`, `role`, etc.) and specific fields for the Atlas integration (`atlasUserId`, `isExternal`, `atlasToken`). The `atlasUserId` is a unique identifier from the Atlas system, linking our user record to their Atlas account. `isExternal` tells us if the user logged in via Atlas or was created manually.

## Protecting Pages with Middleware

Now that the user is logged in and the browser has our application's JWT token (in a cookie), how do we ensure they can access protected pages like the dashboard or their forms, but someone without a valid token cannot?

This is where **Middleware** comes in. Think of Middleware as a **gatekeeper** or **bouncer** that stands in front of every page (or almost every page) on your website. Before a request can even get to the page content, the middleware checks its credentials.

In our application, the main gatekeeper is defined in `middleware.ts`:

```typescript
// Inside middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
// We don't verify the token signature here because of environment limitations.
// Verification happens in server components/API routes.

// Define routes that are PUBLIC (don't need authentication)
const publicPaths = [
  '/login', // The login page itself
  '/api/atlas-auth', // The login API endpoint
  '/api/public', // Any public API endpoints
  // ... other public paths like signup (redirected to login), etc.
];

// Helper to check if a path is public (includes static files)
function isPublicPath(path: string): boolean {
  if (path.startsWith('/_next') || // Next.js internal files
      path.startsWith('/favicon.ico') ||
      path.startsWith('/images') ||
      path.startsWith('/assets') ||
      path.startsWith('/form-auth-handler.js')) { // Specific public scripts
    return true;
  }
  return publicPaths.some(publicPath => path.startsWith(publicPath));
}

// Helper to check if a path is a protected form-related page (like /forms/{id}/dashboard)
function isProtectedFormPath(path: string): boolean {
  const formIdPattern = /^\/forms\/[a-zA-Z0-9]+\/(dashboard|edit)/;
  return formIdPattern.test(path);
}

// Helper to redirect the user to the login page
function redirectToLogin(request: NextRequest, path: string) {
  console.log('Authentication required, redirecting to login:', path);
  const loginUrl = new URL('/login', request.url);
  // Add the current path as a redirect parameter so the user can go back after logging in
  loginUrl.searchParams.set('redirect', path);
  return NextResponse.redirect(loginUrl); // Perform the redirect
}

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;

  // If the requested path is public, let them pass immediately
  if (isPublicPath(path)) {
    return NextResponse.next(); // Allow the request to continue
  }

  // Get our authentication token and user email from cookies
  const token = request.cookies.get('token')?.value;
  const userEmail = request.cookies.get('userEmail')?.value;

  console.log('Middleware checking path:', path, 'Authenticated:', !!token || !!userEmail);

  // Check if the path is a protected form page OR requires general auth
  if (isProtectedFormPath(path) || (!token && !userEmail)) {
      // If it's protected and there's no token or email, redirect to login
      if (!token && !userEmail) {
          console.log('No authentication found for protected path, redirecting');
          return redirectToLogin(request, path);
      }
  }

  // If a token or userEmail is found (even if we don't verify it here),
  // we assume they are logged in *for middleware purposes*.
  // Real verification happens in server components or API routes.
  if (token || userEmail) {
      console.log('Authentication artifact found (token or email), allowing access.');
      return NextResponse.next(); // Allow the request to continue
  }

  // Fallback: If we somehow reached here without token/email on a non-public path, redirect
  console.log('Fallback: No auth artifact found, redirecting to login.');
  return redirectToLogin(request, path);
}

// Define which paths the middleware should run on
export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)'] // Run on almost all paths, excluding static files
};
```

This middleware runs *before* rendering pages or processing API routes. It checks if the requested path is public. If not, it looks for our `token` or `userEmail` cookie. If these aren't found, it redirects the user to the `/login` page. This ensures that only logged-in users (those with the correct cookie) can access protected parts of the application like the dashboard or forms pages.

Why don't we fully verify the JWT token's signature in the middleware? This is a technical limitation in some Next.js environments (like the Edge runtime where middleware runs), which might not fully support the `crypto` module needed for JWT verification. Instead, the middleware performs a basic check for the *presence* of the token/email. The actual verification (checking if the token is valid, not expired, and matches the user) happens in the server components or API routes that handle the data logic.

For example, when a protected page like `/admin` loads (`app/admin/page.tsx`):

```typescript
// Inside app/admin/page.tsx
import { redirect } from "next/navigation";
import AdminDashboard from "@/components/admin/AdminDashboard";
// Import functions to get the current user and check if they are admin
import { getCurrentUser, checkAdminStatus } from "@/lib/actions/user.actions";

export const dynamic = 'force-dynamic'; // Ensure this page always runs server-side

export default async function AdminPage() {
  try {
    // Get the current user based on the cookie/session
    const user = await getCurrentUser();

    // If no user is found (token might be missing or invalid/expired), redirect to login
    if (!user) {
      console.log("Admin page: User not authenticated, redirecting.");
      redirect('/login');
    }

    // Check if the authenticated user has the 'admin' role
    const isAdmin = await checkAdminStatus();

    // If the user is logged in but not an admin, redirect them to a different page
    if (!isAdmin) {
      console.log("Admin page: User is authenticated but not an admin, redirecting.");
      redirect('/dashboard'); // Redirect to dashboard as they aren't authorized for admin page
    }

    // If the user is authenticated AND is an admin, render the Admin Dashboard
    console.log("Admin page: User is authenticated and is admin, rendering dashboard.");
    return <AdminDashboard />;

  } catch (error) {
    console.error("Error in AdminPage:", error);
    // In case of any other error, redirect to dashboard as a fallback
    redirect('/dashboard');
  }
}
```

This page component first uses `getCurrentUser()` to get the details of the logged-in user. The `getCurrentUser` function (`lib/actions/user.actions.ts`) retrieves the token/email from the cookies and then verifies the token's signature and looks up the user in the database.

```typescript
// Inside lib/actions/user.actions.ts (simplified)
import { cookies } from 'next/headers';
import { verify } from 'jsonwebtoken'; // Use 'jsonwebtoken' library
import connectDB from "@/lib/mongodb";
import User from "@/models/User";

// Helper function to get the logged-in user based on cookies
export async function getCurrentUser() {
  try {
    // Get cookies on the server side
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    const userEmail = cookieStore.get('userEmail')?.value;

    // If no token or email cookie, no user is logged in
    if (!token && !userEmail) {
      console.log('getCurrentUser: No token or email cookie found.');
      return null;
    }

    // Get our JWT secret from environment variables
    const jwtSecret = process.env.JWT_SECRET || 'your_jwt_secret_here';

    let decoded = null;
    // Try to verify the token first
    if (token) {
        try {
            decoded = verify(token, jwtSecret) as { userId: string; email: string };
            console.log('getCurrentUser: Token verified successfully.');
        } catch (tokenError) {
            console.error('getCurrentUser: Token verification failed:', tokenError);
            decoded = null; // Invalid or expired token
        }
    }


    // Connect to our database
    await connectDB();

    let user = null;
    // If token was valid and decoded, find user by ID
    if (decoded && decoded.userId) {
      console.log('getCurrentUser: Searching DB by userId:', decoded.userId);
      user = await User.findById(decoded.userId);
      if (user) {
          console.log('getCurrentUser: Found user by userId.');
          return user; // Found user by valid token
      } else {
          console.log('getCurrentUser: User not found by userId, token might be old.');
      }
    }

    // If no user found by ID (either no token, token invalid, or ID not found),
    // try finding user by email (from decoded token or backup cookie)
    const emailToSearch = (decoded?.email || userEmail);
     if (emailToSearch) {
         console.log('getCurrentUser: Searching DB by email:', emailToSearch);
         user = await User.findOne({ email: emailToSearch });
         if (user) {
             console.log('getCurrentUser: Found user by email.');
             return user; // Found user by email
         } else {
             console.log('getCurrentUser: User not found by email.');
         }
     }


    // If we reached here, no valid user was found
    console.log('getCurrentUser: No authenticated user found.');
    return null;

  } catch (error) {
    console.error("Error in getCurrentUser:", error);
    return null; // Return null in case of any unexpected error
  }
}

// Helper function to check if the current user is an admin
export async function checkAdminStatus() {
  try {
    const user = await getCurrentUser();
    // Returns true if user exists and their role is 'admin', otherwise false
    return user?.role === 'admin' || false;
  } catch (error) {
    console.error("Error checking admin status:", error);
    return false;
  }
}

// ... other user actions like getOrCreateUser
```

If `getCurrentUser` returns `null`, the `AdminPage` knows the user isn't properly logged in and redirects them to `/login`. If a user object is returned, `AdminPage` then calls `checkAdminStatus()` (which also uses `getCurrentUser`) to see if their `role` is 'admin'. If not, they are redirected to the dashboard, enforcing **Authorization** (what they are *allowed* to do).

## User Management (Admin Feature)

The "Management" part of "User Authentication & Management" includes features like the admin pages where administrators can view, add, edit, or delete user accounts within our Myform application.

This is handled by components like `components/admin/UsersManagement.tsx`, which displays a list of users and provides buttons to add, edit, or delete them. It interacts with API routes like `/api/admin/users` and `/api/admin/users/[id]`.

```typescript
// Inside components/admin/UsersManagement.tsx (simplified)
'use client';

import { useEffect, useState } from 'react';
// ... other imports

interface User {
  _id?: string; // Database ID
  email: string;
  firstName: string;
  lastName: string;
  role: string; // 'user' | 'admin' etc.
  status: string; // 'active' | 'suspended' etc.
  atlasUserId?: string; // The ID from Atlas
}

export default function UsersManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  // ... state for modals, filtering, etc.

  // Fetch users from our backend API
  const fetchUsers = async () => {
    try {
      // This API route is protected and requires admin role
      const response = await fetch('/api/admin/users');
      if (!response.ok) {
        // Handle non-admin access error here (e.g., show a message, redirect)
        throw new Error('Failed to fetch users or unauthorized');
      }
      const data = await response.json();
      setUsers(data); // Set the list of users
    } catch (error) {
      console.error('Error fetching users:', error);
      // Show an error message to the user
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers(); // Fetch users when the component loads
  }, []);

  // ... functions to handle adding, editing, deleting users
  const handleAddUser = () => { /* ... open modal */ };
  const handleEditUser = (user: User) => { /* ... open modal with user data */ };
  const handleDeleteUser = async (userId: string | undefined) => {
    // ... confirm deletion
    try {
       const response = await fetch(`/api/admin/users/${userId}`, { method: 'DELETE' });
       if (!response.ok) throw new Error('Failed to delete user');
       setUsers(users.filter(user => user._id !== userId)); // Remove from list
    } catch (error) { /* ... handle error */ }
  };

  const handleSubmitUser = async (userData: User) => {
    // ... handle saving new or updated user via POST/PATCH API calls
  };


  return (
    // ... UI for user list, filters, add button
    <div>
      <h2>User Management</h2>
      <button onClick={handleAddUser}>Add New User</button>
      {/* User list rendering */}
      {users.map(user => (
        <div key={user._id}>
          {user.firstName} {user.lastName} ({user.role})
          <button onClick={() => handleEditUser(user)}>Edit</button>
          <button onClick={() => handleDeleteUser(user._id)}>Delete</button>
        </div>
      ))}
      {/* User form modal */}
      {/* ... */}
    </div>
  );
}
```

These admin API routes also use the authentication and authorization checks. For example, the GET route to list all users (`app/api/admin/users/route.ts`):

```typescript
// Inside app/api/admin/users/route.ts (simplified)
import { NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import User from "@/models/User";
import { checkAdminStatus } from "@/lib/actions/user.actions"; // Our admin check function

// Middleware to check if user is admin BEFORE proceeding
async function checkAdmin() {
  const isAdmin = await checkAdminStatus();
  if (!isAdmin) {
    // If not admin, throw an error
    throw new Error('Unauthorized: Admin access required');
  }
}

// Get all users route (GET /api/admin/users)
export async function GET() {
  try {
    // Perform the admin check
    await checkAdmin(); // This will throw if the user isn't admin

    // If the check passes, connect to DB and fetch users
    await connectDB();
    const users = await User.find({}).select('-settings -__v'); // Fetch users (exclude sensitive/internal fields)
    return NextResponse.json(users); // Return the list of users

  } catch (error: any) {
    console.error('Error fetching users (admin route):', error);
    // If checkAdmin threw an error, catch it and return 401/403
    if (error.message.includes('Unauthorized')) {
         return new NextResponse(error.message, { status: 403 }); // Use 403 Forbidden for authorization failure
    }
    return new NextResponse(error.message || 'Internal Server Error', { status: 500 });
  }
}

// ... POST route for creating a new user (also uses checkAdmin)
```

This demonstrates how `checkAdminStatus()` is used to protect the admin functionality, ensuring only users with the 'admin' role can perform these actions.

## Conclusion

In this chapter, we explored the essential concepts of User Authentication and Management in Myform. We learned how users log in using their Atlas University credentials via a process involving client-side components, a dedicated backend API route that interacts with the Atlas API and our own database, and the issuance of a JWT token stored in cookies. We also saw how Next.js Middleware acts as a gatekeeper to protect routes and how server-side code ([Next.js API Routes](05_next_js_api_routes_.md), [Database Connection & Models](06_database_connection___models_.md), `lib/actions`) verifies the user's identity and role to provide authorization.

This system ensures that Myform is secure, allowing only authorized users to access its features and data.

Now that we understand who can use Myform, let's move on to the core functionality: building and managing forms.

[Chapter 2: Form Structure & Management
](02_form_structure___management_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)