# Chapter 7: Global Middleware

Welcome back to the Myform tutorial! In the previous chapter, [Chapter 6: Database Connection & Models](06_database_connection___models_.md), we learned how Myform connects to its database and structures the information it stores, like users, forms, and responses.

Now that we know *where* the data lives, how do we control *who* gets to see or interact with it? We need a system that acts as the first line of defense, checking permissions *before* a user even reaches a page or triggers a backend action.

This is where **Global Middleware** comes in!

Think of Middleware in Myform like the **security guard or bouncer** at the entrance of a building. Every time someone tries to enter (request a page) or access a restricted area (call a protected API route), the guard is the very first person they encounter.

The guard's job is simple but critical:

1.  Check their **credentials** (Are they supposed to be here? Are they logged in?).
2.  If the credentials are valid, **let them pass** to their destination.
3.  If the credentials are missing or invalid, **stop them** and tell them they need to go to the **login area** first.
4.  However, some areas like the main entrance or a public reception area are **always accessible**, even without special credentials.

In Myform, the Global Middleware is a special function that runs very early in the request process. Its main role is to check if a user is logged in by looking for their authentication token (which was set as a cookie during login in [Chapter 1: User Authentication & Management](01_user_authentication___management_.md)). If the user isn't logged in and they are trying to access a page or API that requires authentication, the Middleware redirects them straight to the login page.

This ensures that protected parts of your application are not directly accessible to just anyone browsing the web.

## What is Next.js Middleware?

In Next.js, you create Middleware by adding a special file named `middleware.ts` (or `middleware.js`) at the root of your project or inside the `src` directory.

This file exports a single `middleware` function that Next.js runs for incoming requests *before* deciding which page or API Route handler to execute.

## Myform's Authentication Gatekeeper

Myform uses `middleware.ts` to implement the security guard logic described above. It checks if a user is logged in based on the presence of the `token` and `userEmail` cookies that are set when a user successfully logs in via the Atlas API ([Chapter 1](01_user_authentication___management_.md)).

Here's the core job of Myform's `middleware.ts`:

*   Allow requests for **public paths** (like `/login` or `/api/atlas-auth`) to pass through immediately.
*   For all other paths, check if the `token` or `userEmail` cookie exists.
*   If the cookie exists, allow the request to continue to the intended page or API route.
*   If the cookie is missing *and* the path is not public, redirect the user to the `/login` page.

## How Myform's Middleware Works (Simplified)

Let's look at the key parts of the `middleware.ts` file to understand how this gatekeeping works.

First, we need to define which paths should *always* be accessible without authentication:

```typescript
// Inside middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define public routes that don't require authentication
const publicPaths = [
  '/login',             // The login page itself
  '/api/atlas-auth',    // The login API endpoint
  '/api/public',        // Any public API endpoints
  '/api/check-auth',    // API to check auth status from client
  '/api/check-api-keys', // API to check if keys are configured (for development login)
  '/sign-in',           // Alternative login path (redirects to /login)
  '/sign-up',           // Alternative signup path (redirects to /login)
  '/test-login',        // Development test login page
  // ... add other publicly accessible paths here
];

// Helper function to check if a path is public
function isPublicPath(path: string): boolean {
  // Allow Next.js internal files and static assets like images, favicon
  if (path.startsWith('/_next') ||
      path.startsWith('/favicon.ico') ||
      path.startsWith('/images') ||
      path.startsWith('/assets') ||
      path.startsWith('/form-auth-handler.js')) { // Specific script needed publicly
    return true;
  }

  // Check against our list of defined public paths
  return publicPaths.some(publicPath => path.startsWith(publicPath));
}

// Other helper functions will be defined later...
```

The `publicPaths` array lists all the entry points and resources that anyone should be able to access. The `isPublicPath` function checks if the requested `path` matches any of these, including standard Next.js internal paths and static files.

Next, the main `middleware` function uses this helper:

```typescript
// Inside middleware.ts
// ... (imports and isPublicPath function from above) ...

// Helper to redirect the user to the login page
function redirectToLogin(request: NextRequest, path: string) {
  console.log('Authentication required, redirecting to login:', path);
  const loginUrl = new URL('/login', request.url);
  // Add the current path as a 'redirect' parameter so the user
  // can be sent back here after successfully logging in.
  loginUrl.searchParams.set('redirect', path);
  return NextResponse.redirect(loginUrl); // This tells Next.js to redirect
}

// This is the main Middleware function that Next.js runs
export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname; // Get the path the user is requesting

  console.log('Middleware checking path:', path);

  // If the path is public, let the request proceed immediately
  if (isPublicPath(path)) {
    console.log('Path is public, allowing access.');
    return NextResponse.next(); // Allow the request to continue to the page/API
  }

  // If the path is NOT public, check for authentication artifacts (token/email cookie)
  const token = request.cookies.get('token')?.value;
  const userEmail = request.cookies.get('userEmail')?.value;

  console.log('Middleware checking authentication for protected path:', path);
  console.log('  Has Token:', !!token, 'Has Email:', !!userEmail);

  // If NO token AND NO email cookie is found, redirect to login
  if (!token && !userEmail) {
    console.log('No authentication artifact found, redirecting to login.');
    return redirectToLogin(request, path); // Redirect the user
  }

  // If a token OR email cookie IS found, we assume they are logged in
  // for the purpose of the Middleware. We allow the request to continue.
  // The actual verification of the token's validity and expiry
  // happens later in server components or API routes (see Chapter 1).
  console.log('Authentication artifact found, allowing access.');
  return NextResponse.next(); // Allow the request to continue

  // Note: The provided code includes an additional check for 'isProtectedFormPath'.
  // This adds a specific check for pages like /forms/:id/dashboard or /forms/:id/edit.
  // If you uncomment or add this logic, ensure the check for !token && !userEmail
  // covers these paths as well, as shown in the full middleware.ts file.
}

// This tells Next.js to run this middleware on almost all paths,
// except for internal Next.js files and static assets.
export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)']
};
```

**Step-by-step explanation:**

1.  **`export async function middleware(request: NextRequest)`**: This defines the asynchronous middleware function that receives the incoming request details.
2.  **`const path = request.nextUrl.pathname;`**: Extracts the URL path the user is trying to visit (e.g., `/dashboard`, `/forms/abc123`, `/api/forms`).
3.  **`if (isPublicPath(path))`**: Checks if this path is one of the exceptions listed in our `publicPaths`.
4.  **`return NextResponse.next();`**: If it's a public path, the middleware says "okay, proceed" and the request continues its normal flow to the page or API route handler.
5.  **`const token = request.cookies.get('token')?.value;`**: If the path is *not* public, it tries to read the `token` and `userEmail` cookies from the incoming request. These cookies were placed there by our login API route when the user authenticated ([Chapter 1](01_user_authentication___management_.md)).
6.  **`if (!token && !userEmail)`**: This is the main check. If *neither* the token *nor* the email cookie is found, it means the user doesn't have the required credentials.
7.  **`return redirectToLogin(request, path);`**: If the user is not authenticated, the middleware redirects them to the `/login` page. It's important to use `NextResponse.redirect()` for this. We also add the original path they tried to visit as a `redirect` search parameter, so the login page knows where to send them *after* they log in successfully.
8.  **`if (token || userEmail)`**: If *either* the token or email cookie *is* found, the middleware assumes the user is *potentially* logged in. For basic gatekeeping, this presence check is sufficient in the middleware. The request is allowed to continue using `NextResponse.next()`.
9.  **`export const config = { matcher: [...] }`**: This configuration tells Next.js *when* to run this middleware. The `matcher` pattern `'/((?!_next/static|_next/image|favicon.ico).*)}` is a regular expression that basically says "run this middleware for any path, *unless* it starts with `/_next/static`, `/_next/image`, or is `/favicon.ico`". This ensures the middleware runs on your pages and API routes but not on static files.

## Why Not Full JWT Verification in Middleware?

You might wonder why we don't fully verify the JWT token's signature and expiry directly within this `middleware.ts` file. While ideal for strong security checks *at the edge*, Next.js Middleware often runs in environments like the Edge Runtime, which might have limitations, such as not fully supporting Node.js's `crypto` module needed for JWT signature verification.

Therefore, Myform's strategy (as described in [Chapter 1](01_user_authentication___management_.md)) is:

1.  **Middleware (`middleware.ts`):** Performs a **basic check** for the *presence* of the authentication cookie (`token` or `userEmail`). This is fast and sufficient for simple redirects.
2.  **Server Components / API Routes ([Chapter 5](05_next_js_api_routes_.md)):** When accessing protected data or performing sensitive actions, the server-side code (specifically functions like `getCurrentUser` from `lib/actions/user.actions.ts`, as seen in [Chapter 1](01_user_authentication___management_.md)) performs the **full JWT verification** (checking signature, expiry, and looking up the user in the database). If this deeper check fails, *that* code is responsible for denying access or redirecting.

This layered approach provides a good balance of performance (fast initial gatekeeping in middleware) and robust security (full verification where sensitive actions occur).

## Connecting to Other Concepts

Global Middleware plays a foundational role, interacting with:

*   **[Chapter 1: User Authentication & Management](01_user_authentication___management_.md):** Middleware relies entirely on the `token` and `userEmail` cookies that are the result of the login process handled by the `/api/atlas-auth/login` route and the `AuthContext`.
*   **[Chapter 5: Next.js API Routes](05_next_js_api_routes_.md):** Middleware runs before API Routes. This means it protects sensitive API endpoints (like `/api/forms` or `/api/admin/users`) from unauthorized access *before* their code even begins to execute.
*   **[Chapter 6: Database Connection & Models](06_database_connection___models_.md):** By protecting the pages and API routes that interact with the database (fetching users, forms, responses), the Middleware indirectly protects the data stored in MongoDB, ensuring that only logged-in users can potentially trigger database operations.

## Conclusion

In this chapter, we learned about **Global Middleware** in Next.js and how Myform uses it as the primary authentication gatekeeper. We saw how the `middleware.ts` file checks incoming requests, identifies public paths, looks for authentication cookies, and redirects unauthenticated users to the login page before they can access protected content. We also understood the layered security approach where Middleware provides initial presence checking, while server components and API routes perform full verification.

Understanding Middleware is key to seeing how Myform enforces a baseline level of security across the application, ensuring that only users who have gone through the authentication process ([Chapter 1](01_user_authentication___management_.md)) can access most features.

Now that we have explored authentication, forms, responses, AI, API routes, databases, and middleware, let's look at how Myform can adapt to different languages to reach a wider audience.

[Chapter 8: Localization (i18n)](08_localization__i18n__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)