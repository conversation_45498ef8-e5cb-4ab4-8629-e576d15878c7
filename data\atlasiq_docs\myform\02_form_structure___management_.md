# Chapter 2: Form Structure & Management

Welcome back to the Myform tutorial! In the last chapter, [Chapter 1: User Authentication & Management
](01_user_authentication___management_.md), we learned how Myform knows *who* you are – how you log in securely using your Atlas University credentials and how the application keeps track of your identity.

Now that we know *who* is using the application, let's talk about the main thing they're using it for: **Forms!**

Think about it: Myform is all about creating forms and surveys. But what exactly *is* a form in the world of Myform? How does the application remember what questions are in a form, whether a question is required, or what options are available for a multiple-choice question?

This is where **Form Structure & Management** comes in.

It's like the **blueprint** for every form created in Myform. It defines:

*   What the form is named (e.g., "Student Feedback Survey").
*   What kind of form it is (a simple 'form' or a 'survey').
*   A description of the form.
*   And most importantly, the **list of questions or input fields** it contains.

It's also the **storage system** that holds all these blueprints so users can access and manage *their* forms.

## What Does a Form Look Like (Its Structure)?

Imagine you're building a physical form using paper and pen. You'd give it a title, maybe a description, and then list out the questions. For each question, you'd decide:

*   What's the question asking? (The Label)
*   What kind of answer do you need? (Text, Number, Checkbox, Date, etc. - The Type)
*   Is answering this question mandatory? (Required)
*   If it's a multiple-choice question, what are the options? (Options)

In Myform, a digital form has a very similar structure. This structure is defined in our code, specifically in something called a **Mongoose Model**. We'll talk more about Mongoose Models and databases in [Chapter 6: Database Connection & Models
](06_database_connection___models_.md), but for now, think of the `Form` model as the blueprint for storing a form in our database.

Here's a simplified look at the `Form` model structure (`models/Form.ts`):

```typescript
// Simplified structure from models/Form.ts

import mongoose from 'mongoose';

// How we define the options for questions like Select or Radio
const formOptionSchema = new mongoose.Schema({
  label: { type: String, required: true }, // What the user sees (e.g., "Yes")
  value: { type: String, required: true }, // The value saved (e.g., "yes")
  description: String, // Optional extra info
}, { _id: false }); // We don't need a separate ID for each option


// How we define each individual question/input field within a form
const formFieldSchema = new mongoose.Schema({
  name: { type: String, required: true }, // A unique identifier for the field (e.g., "student_id")
  label: { type: String, required: true }, // The question text (e.g., "Please enter your Student ID:")
  type: { // What kind of input is it?
    type: String,
    required: true,
    enum: ['text', 'number', 'email', 'select', 'radio', 'checkbox', /* ... other types */]
  },
  required: { type: Boolean, default: false }, // Is this field mandatory?
  options: [formOptionSchema], // Array of options for select/radio/checkbox
  placeholder: String, // Hint text inside the input
  description: String, // Helper text below the field
  // ... other properties like validation rules, styling, etc.
}, { _id: false, strict: false }); // No separate ID for fields, strict: false allows extra properties


// The main schema for a Form
const formSchema = new mongoose.Schema({
  name: { type: String, required: true }, // The form's title
  type: { type: String, required: true, enum: ['form', 'survey'] }, // Is it a 'form' or 'survey'?
  description: String, // Overall description of the form
  formFields: [formFieldSchema], // THIS IS THE KEY: An array of the fields defined above
  isPublished: { type: Boolean, default: false }, // Can people currently fill out this form?
  publishedUrl: String, // The public link if published
  responses: { type: Number, default: 0 }, // How many times has it been submitted? (Basic count)
  responseCount: { type: Number, default: 0 }, // Another count field (can be synced)
  userId: { type: String, required: true }, // Who created this form? (Links to the User model)
  userEmail: { type: String, index: true }, // Creator's email (also for linking)
  generatedJson: { type: mongoose.Schema.Types.Mixed, required: true }, // A simplified JSON version (maybe for frontend)
  // ... other fields like timestamps, settings, style, etc.
}, {
  timestamps: true // Automatically adds createdAt and updatedAt fields
});

// Create the Mongoose model
const Form = mongoose.models.Form || mongoose.model('Form', formSchema);

export default Form;
```

As you can see, the `FormSchema` contains basic info like `name`, `type`, and `description`. The most important part is the `formFields` array. This array holds multiple `formFieldSchema` objects, and each object describes one specific input field or question within that form. This is how the application knows the complete structure of any given form.

Notice the `userId` and `userEmail` fields on the main `FormSchema`. These fields are crucial because they link a form back to the user who created it. This is how Myform knows which forms belong to *you*.

## Managing Your Forms (Use Case Walkthrough)

Let's follow the use case from the user's perspective: **A user wants to see and manage their forms.**

When a user logs in (thanks to [Chapter 1: User Authentication & Management
](01_user_authentication___management_.md)) and navigates to the "My Forms" page, what happens?

1.  The browser loads the "My Forms" page component (`app\forms\page.tsx`).
2.  This component needs to fetch the list of forms that belong *specifically* to the logged-in user.
3.  It uses client-side code (in `app\forms\FormsClient.tsx`) to make a request to the backend API.

Here's a simplified look at the client-side component fetching the forms (`app\forms\FormsClient.tsx`):

```typescript
// Inside app\forms\FormsClient.tsx (Simplified)
"use client"; // This component runs in the user's browser

import { useState, useEffect } from 'react';
import Link from 'next/link';
// ... other imports for icons and UI

// Define the structure of a form we expect from the API
interface Form {
  _id: string; // The unique ID from the database
  name: string;
  type: 'form' | 'survey';
  responses: number; // How many responses it has
  updatedAt: string; // Last time it was modified
  isPublished: boolean; // Is it public?
  // ... other basic fields needed for the list view
}

export default function FormsClient() {
  // State to hold the list of forms
  const [forms, setForms] = useState<Form[]>([]);
  // State to track if data is loading
  const [loading, setLoading] = useState(true);
  // State to track errors
  const [error, setError] = useState<string | null>(null);

  // useEffect runs when the component is first shown
  useEffect(() => {
    fetchForms(); // Call the function to get forms from the backend
  }, []); // The empty array means this effect runs only once on mount

  const fetchForms = async () => {
    try {
      const response = await fetch('/api/forms'); // Make a request to our backend API

      // Check if the response was successful
      if (!response.ok) {
        // If not OK, throw an error (e.g., 401 Unauthorized, 500 Server Error)
        throw new Error('Failed to fetch forms');
      }

      const data = await response.json(); // Parse the JSON data from the response
      setForms(data); // Update the state with the list of forms
    } catch (error) {
      console.error('Error fetching forms:', error);
      setError('Failed to load forms. Please try again later.'); // Show error message
    } finally {
      setLoading(false); // Turn off loading state
    }
  };

  // ... functions for handleDelete, handleDuplicate, handlePublish etc.
  // ... UI rendering (displaying the list of forms, buttons)

  if (loading) {
      return <div>Loading forms...</div>; // Show loading message
  }

  if (error) {
      return <div>Error: {error}</div>; // Show error message
  }


  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header with Create button */}
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">My Forms</h1>
        {/* Link to the AI Form Builder feature */}
        <Link href="/features/ai-form-builder/generate" className="bg-blue-600 text-white px-4 py-2 rounded">
          + Create New Form
        </Link>
      </div>

      {/* List of Forms */}
      <div className="bg-white rounded-lg shadow">
         {forms.map(form => (
            <div key={form._id} className="border-b p-4 flex items-center justify-between">
                <div>
                    <div className="font-semibold">{form.name}</div>
                    <div className="text-sm text-gray-500">{form.type} - {form.responses} Responses</div>
                </div>
                {/* Action Buttons */}
                <div className="flex gap-2">
                    {/* Link to edit the form structure */}
                    <Link href={`/forms/${form._id}/edit`} className="text-blue-600 hover:underline">Edit</Link>
                    {/* Button to delete the form */}
                    <button onClick={() => handleDelete(form._id)} className="text-red-600 hover:underline">Delete</button>
                    {/* Button to toggle publish status */}
                    <button onClick={() => handlePublish(form._id, !form.isPublished)} className="text-green-600 hover:underline">
                        {form.isPublished ? 'Unpublish' : 'Publish'}
                    </button>
                     {/* Link to view responses (covered in next chapter) */}
                    <Link href={`/forms/${form._id}/dashboard`} className="text-purple-600 hover:underline">Responses</Link>
                </div>
            </div>
         ))}
         {forms.length === 0 && (
             <div className="p-8 text-center text-gray-500">No forms created yet.</div>
         )}
      </div>
    </div>
  );
}
```

This component is quite simple on the surface: it fetches data when it loads and then displays that data as a list. The magic happens in the call to `/api/forms`.

## Under the Hood: Fetching and Managing Forms

How does the `/api/forms` route know which forms to return? And how do the other actions (creating, editing, deleting) work?

This is where our backend API routes and the database model come together.

Here's a sequence diagram showing how fetching forms works:

```mermaid
sequenceDiagram
    participant UserBrowser as User (Browser)
    participant FormsClient as FormsClient (Client Component)
    participant FormsAPIRoute as Backend API (/api/forms GET)
    participant UserActions as User Actions (getCurrentUser)
    participant Database as Our Database (MongoDB)

    UserBrowser->>FormsClient: Navigate to My Forms page
    FormsClient->>FormsClient: Component mounts, calls fetchForms()
    FormsClient->>FormsAPIRoute: GET request to /api/forms
    FormsAPIRoute->>UserActions: Call getCurrentUser()
    UserActions->>UserActions: Read token/email from cookie
    UserActions->>Database: Find user by ID/Email
    Database-->>UserActions: Return User data
    UserActions-->>FormsAPIRoute: Return User object
    FormsAPIRoute->>FormsAPIRoute: Check if user is logged in
    alt User is Logged In
        FormsAPIRoute->>Database: Find Forms where userId/userEmail matches User
        Database-->>FormsAPIRoute: Return list of Forms
        FormsAPIRoute-->>FormsClient: Send list of Forms (JSON)
        FormsClient->>FormsClient: Update state with forms
        FormsClient->>UserBrowser: Display the list of forms
    else User Not Logged In
        FormsAPIRoute-->>FormsClient: Send 401 Unauthorized response
        FormsClient->>FormsClient: Handle error, potentially redirect to login
        FormsClient->>UserBrowser: Show error or login page
    end
```

The core logic for managing forms lives within the `/api/forms/route.ts` file for actions that affect *many* forms (like listing or creating) or the `/api/forms/[id]/route.ts` file for actions on a *specific* form (like getting, editing, or deleting by ID). There are also admin-specific routes like `/api/admin/forms`, which we'll touch on briefly.

Let's look at simplified examples from these API routes:

**1. Fetching All Forms for the Current User (`app\api\forms\route.ts` - GET)**

```typescript
// Inside app\api\forms\route.ts (Simplified GET)
import { NextResponse } from 'next/server';
import { Form } from '@/app/models/Form'; // Import our Form model
import connectDB from '@/lib/mongodb'; // Function to connect to DB
import { getCurrentUser } from '@/lib/actions/user.actions'; // Function to get logged-in user

export async function GET(request: any) {
  try {
    await connectDB(); // Connect to the database
    
    // Get the current user from their authentication token/cookie
    const user = await getCurrentUser();
    
    // If no user is found, they are not logged in
    if (!user) {
      console.log('Unauthorized access to /api/forms GET');
      return NextResponse.json(
        { error: 'Unauthorized - Please log in' },
        { status: 401 } // 401 status code means Unauthorized
      );
    }
    
    const userId = user._id;
    const userEmail = user.email;

    // Find forms in the database that belong to THIS user
    // We check both userId (database ID) and userEmail for flexibility/migration
    const query: any = {
      $or: [
        { userEmail: userEmail },
      ]
    };
    
    if (userId) {
      query.$or.push({ userId: userId });
    }
    // Add clerkId to query if available (from previous chapter context)
    if (user.clerkId) {
      query.$or.push({ userId: user.clerkId });
    }
    
    console.log('Fetching forms for user query:', JSON.stringify(query));
    // Use our Form model to find documents matching the user ID/email
    const forms = await Form.find(query).sort({ createdAt: -1 }); // Find and sort by creation date

    console.log(`Found ${forms.length} forms for user`);
    return NextResponse.json(forms); // Send the found forms back to the client

  } catch (error: any) {
    console.error('Error fetching forms:', error);
    return NextResponse.json(
      { error: error.message || 'Error fetching forms' },
      { status: 500 } // 500 status code means Internal Server Error
    );
  }
}
```

This code connects to the database and, crucially, uses `getCurrentUser()` (which we discussed in [Chapter 1: User Authentication & Management
](01_user_authentication___management_.md)) to identify the logged-in user. It then queries the `Form` collection in the database, filtering the results to only include forms where the `userId` or `userEmail` matches the current user's details. This ensures users only see *their* forms on the "My Forms" page.

**2. Creating a New Form (`app\api\forms\route.ts` - POST)**

When a user creates a new form (e.g., via the AI builder mentioned in [Chapter 4: AI Integration
](04_ai_integration_.md) or a manual builder), the client sends a POST request to this route with the form's structure.

```typescript
// Inside app\api\forms\route.ts (Simplified POST)
import { NextResponse } from 'next/server';
import { Form } from '@/app/models/Form'; // Import our Form model
import connectDB from '@/lib/mongodb'; // Function to connect to DB
import { getCurrentUser } from '@/lib/actions/user.actions'; // Function to get logged-in user
// Import helper for processing fields (e.g., rating options) - see code snippet below
import { processFormData } from './process-rating-fields';

export async function POST(request: any) {
  try {
    await connectDB(); // Connect to the database
    
    // Get the current user - essential for associating the form
    const user = await getCurrentUser();
    if (!user) {
      console.error('Unauthorized access to /api/forms POST');
      return NextResponse.json(
        { error: 'Unauthorized - Please log in', redirectTo: '/login' },
        { status: 401 }
      );
    }
    
    // Get the form data (name, description, fields, etc.) from the request body
    let formData:any = await request.json();
    console.log('Received form data:', { name: formData.name, type: formData.type });

    // Associate the form with the current user
    formData.userId = user._id; // Link by user's database ID
    formData.userEmail = user.email; // Link by user's email
    // Optionally add clerkId if used for userId in some cases
    if (user.clerkId) {
        formData.userId = user.clerkId; // Use clerkId if available/preferred
    }


    // Basic validation (check if name and fields exist)
    if (!formData.name || !Array.isArray(formData.formFields) || formData.formFields.length === 0) {
      return NextResponse.json(
        { error: 'Form must have a name and at least one field' },
        { status: 400 } // 400 status code means Bad Request
      );
    }
    
    // Process fields (like ensuring rating fields have default options)
    formData = processFormData(formData);
    console.log('Processed form data');


    // Create a new Form document using our Mongoose model
    const form = new Form(formData);

    // Save the new form document to the database
    console.log('Saving new form to database...');
    await form.save();
    console.log('Form saved successfully:', form._id);


    // Return the newly created form (or just success status)
    return NextResponse.json({
      success: true,
      formId: form._id.toString(),
      form: form.toJSON() // Return the saved form data
    });

  } catch (error: any) {
    console.error('Error creating form:', error);
    return NextResponse.json(
      { error: error.message || 'Error creating form' },
      { status: 500 }
    );
  }
}
```

This code snippet shows that when a new form is created, we again get the `user` using `getCurrentUser()` and attach their ID/email (`user.userId`, `user.userEmail`) to the `formData` before saving it to the database using `await form.save()`. This is how the new form is correctly linked to its creator. The `processFormData` helper (shown partially in the provided code snippets) is an example of internal logic to ensure data consistency, like making sure rating fields have default options if none are provided.

**3. Editing a Form (`app\api\forms\[id]\route.ts` - PUT)**

When a user clicks "Edit" on a form, they navigate to `/forms/:formId/edit`. The `EditFormClient.tsx` component (partially shown in the context) fetches the specific form data using `/api/forms/:formId` (GET, similar to the previous GET but for a single ID) and then allows modifications. When the user clicks "Save Changes", it sends a PUT request to `/api/forms/:formId`.

```typescript
// Inside app\api\forms\[id]\route.ts (Simplified PUT)
import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Form from '@/models/Form'; // Import our Form model

export async function PUT(
  request: Request,
  context: { params: { id: string } } // How Next.js gives us the dynamic ID
) {
  try {
    await connectDB(); // Connect to the database
    const formId = context.params.id; // Get the form ID from the URL

    // Get the updated form data from the request body
    const body = await request.json();
    
    // Basic validation (check required fields in the update)
    if (!body.name || !body.type || !body.formFields) {
         return NextResponse.json(
            { error: 'Missing required fields in update' },
            { status: 400 }
         );
    }


    // Find the form by its ID and update it with the new data
    // Note: A full implementation might also check ownership here
    const updatedForm = await Form.findByIdAndUpdate(
      formId,
      {
        name: body.name,
        description: body.description,
        type: body.type,
        formFields: body.formFields, // Update the list of fields!
        // ... potentially update settings, style, generatedJson, etc.
        updatedAt: new Date(), // Update the timestamp
      },
      { new: true, runValidators: true } // Options: return the updated doc, run schema validators
    );

    if (!updatedForm) {
      return NextResponse.json(
        { error: 'Form not found' },
        { status: 404 } // 404 status code means Not Found
      );
    }

    console.log('Form updated successfully:', updatedForm._id);
    return NextResponse.json(updatedForm); // Send the updated form back

  } catch (error: any) {
    console.error('Error updating form:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update form' },
      { status: 500 }
    );
  }
}
```

This route receives the `formId` from the URL and the updated form data in the request body. It uses `Form.findByIdAndUpdate` to locate the specific form in the database by its ID and replace its details (including the `formFields` array) with the new data.

**4. Deleting a Form (`app\api\forms\route.ts` - DELETE)**

Back on the "My Forms" page, clicking the "Delete" button triggers a DELETE request to `/api/forms?id=...`.

```typescript
// Inside app\api\forms\route.ts (Simplified DELETE)
import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Form from '@/models/Form'; // Import our Form model
import { getCurrentUser } from '@/lib/actions/user.actions'; // Function to get logged-in user

export async function DELETE(request: any) {
  try {
    await connectDB(); // Connect to the database
    
    // Get the current user - crucial for ownership check
    const user = await getCurrentUser();
    if (!user) {
      console.error('Unauthorized access to /api/forms DELETE');
      return NextResponse.json(
        { error: 'Unauthorized - Please log in', redirectTo: '/login' },
        { status: 401 }
      );
    }
    
    // Get the form ID from the URL query parameters
    const { searchParams } = new URL(request.url);
    const formId = searchParams.get('id');

    if (!formId) {
      return NextResponse.json(
        { error: 'Form ID is required' },
        { status: 400 }
      );
    }

    // Check if the form exists AND belongs to the current user before deleting
    const result = await Form.deleteOne({
      _id: formId,
      $or: [ // Ensure ownership matches the current user
        { userEmail: user.email },
        { userId: user._id },
        ...(user.clerkId ? [{ userId: user.clerkId }] : [])
      ]
    });

    // If no document was deleted, it means either the ID was wrong or the user didn't own it
    if (result.deletedCount === 0) {
      console.warn(`Attempted to delete form ${formId} by user ${user._id}/${user.email}, but form not found or not owned.`);
      return NextResponse.json(
        { error: 'Form not found or you do not have permission to delete it' },
        { status: 404 } // Use 404 if not found/owned, 403 could also be appropriate for ownership
      );
    }

    console.log(`Form ${formId} deleted successfully by user ${user._id}`);
    return NextResponse.json({ success: true }); // Indicate success

  } catch (error: any) {
    console.error('Error deleting form:', error);
    return NextResponse.json(
      { error: error.message || 'Error deleting form' },
      { status: 500 }
    );
  }
}
```

Similar to fetching and creating, the delete operation first identifies the user. Then, it attempts to delete the form *only if* its `_id` matches the requested `formId` *and* its `userId` or `userEmail` matches the current user. This is a critical security measure to prevent users from deleting other people's forms.

## Admin Management of Forms

Beyond individual users managing their own forms, administrators in Myform also have the ability to view and manage *all* forms in the system. This is handled by separate API routes, typically under an `/api/admin` path.

The core difference is that these admin routes use the `checkAdminStatus()` function (also from [Chapter 1: User Authentication & Management
](01_user_authentication___management_.md)) to verify if the logged-in user has the 'admin' role *before* allowing the operation.

Here's a peek at how the admin route to get all forms works (`app\api\admin\forms\route.ts` - GET):

```typescript
// Inside app\api\admin\forms\route.ts (Simplified GET)
import { NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import { checkAdminStatus } from "@/lib/actions/user.actions"; // Our admin check

export async function GET() {
  try {
    // *** IMPORTANT: Check if the user is an admin FIRST ***
    const isAdmin = await checkAdminStatus();
    if (!isAdmin) {
      console.warn('Attempted unauthorized access to /api/admin/forms GET');
      return new NextResponse('Unauthorized', { status: 401 }); // Or 403 Forbidden
    }
    // *****************************************************

    await connectDB(); // Connect to the database

    // Since it's an admin route, we fetch ALL forms
    // (The real code might use aggregation to include user names, as shown in context)
    const forms = await Form.find({}).sort({ createdAt: -1 });

    console.log(`Admin fetched ${forms.length} forms.`);
    return NextResponse.json(forms); // Return ALL forms

  } catch (error: any) {
    console.error('Error fetching all forms (admin route):', error);
    return new NextResponse(error.message || 'Internal Server Error', { status: 500 });
  }
}
```

This shows the same pattern: connect to the database, but the *very first* check is whether the user is an admin using `checkAdminStatus()`. If they are, the query fetches all forms without filtering by a specific user ID. Admin routes for deleting or updating forms would similarly start with `checkAdminStatus()`.

## Conclusion

In this chapter, we delved into the core concept of **Form Structure & Management** in Myform. We saw that a form is much more than just a name; it's a detailed structure defined by its fields, each with its own type, label, options, and validation rules, stored using a Mongoose model in our database.

We walked through the process of a user managing their forms, understanding how the client-side components interact with backend API routes. Crucially, we saw how these API routes use the authentication information from [Chapter 1: User Authentication & Management
](01_user_authentication___management_.md) (specifically the `getCurrentUser()` function) to ensure users can only access and manage *their own* forms, while admin routes use `checkAdminStatus()` to manage all forms.

This structure and the associated management API routes provide the foundation for users to create, view, edit, and delete the questionnaires that are central to the Myform application.

Now that we know how forms are built and stored, let's explore what happens when people actually fill them out.

[Chapter 3: Form Responses & Analytics
](03_form_responses___analytics_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)