# Chapter 4: AI Integration

Welcome back to the Myform tutorial! In the last chapter, [Chapter 3: Form Responses & Analytics](03_form_responses___analytics_.md), we explored how Myform collects, stores, and lets you view the answers people submit to your forms.

Now, let's look at a feature that makes creating forms much, much easier: **AI Integration**.

Imagine you need to create a form for a specific purpose, like registering attendees for an event or collecting feedback on a workshop. Instead of manually adding each field one by one, wouldn't it be amazing to just **tell Myform what you need** or even **upload an existing document** (like a paper form or registration sheet) and have the application draft the form structure for you?

This is exactly what the AI Integration feature does!

Think of it as having a **super-smart assistant** right inside Myform. You give it instructions or a document, and it uses Artificial Intelligence (like models from OpenAI or Google Gemini) to suggest or create the initial form structure, saving you a lot of time and effort.

Our main goal in this chapter is to understand: **How does Myform use AI to help users create forms, either from a text description or by analyzing a document?**

Let's look at the two main ways AI is integrated:

1.  **Generating from Text:** Describing the form you want.
2.  **Analyzing a Document:** Uploading a file and extracting fields.

## Use Case 1: Generating a Form from a Description

Let's follow the use case: **A user wants to create a form for event registration using AI by describing it.**

The user navigates to the AI Form Builder feature (perhaps `/features/ai-form-builder/generate`), sees a text area, and types something like: "Create a registration form for a workshop. I need fields for name, email, phone number, and a question asking about dietary restrictions with options."

Here's what happens:

1.  The user types their request into a text input field on the client-side page (`app\features\ai-form-builder\generate\page.tsx`).
2.  They select whether they want a 'form' or a 'survey'.
3.  They click the "Generate" button.
4.  The client-side code collects the text description and the chosen type, and sends it to a backend API route (`/api/generate-form`).

Here's a simplified look at the client component (`app\features\ai-form-builder\generate\page.tsx`) handling this:

```typescript
// Inside app\features\ai-form-builder\generate\page.tsx (Simplified)
'use client';

import { useState } from 'react';
// ... other imports

export default function GeneratePage() {
  // State to hold the user's description
  const [prompt, setPrompt] = useState('');
  // State to hold the AI's generated form structure
  const [generatedForm, setGeneratedForm] = useState<any>(null);
  // State for loading indicator
  const [loading, setLoading] = useState(false);
  // State for errors
  const [error, setError] = useState<string | null>(null);
  // State for form or survey type
  const [generationType, setGenerationType] = useState<'form' | 'survey'>('form');

  // Function called when the Generate button is clicked
  const handleGenerateContent = async () => {
    // Basic validation
    if (!prompt.trim()) {
      alert('Please enter a description.');
      return;
    }

    setLoading(true); // Show loading state
    setError(null); // Clear previous errors
    setGeneratedForm(null); // Clear previous result

    try {
      console.log('Sending form generation request to AI API...');
      // Make the POST request to our backend API route
      const response = await fetch("/api/generate-form", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        // Include cookies to pass authentication info (Chapter 1)
        credentials: 'include',
        body: JSON.stringify({
          prompt, // The user's text description
          type: generationType, // 'form' or 'survey'
          provider: 'gemini', // Example: specify AI provider
          model: 'gemini-1.5-pro', // Example: specify AI model
          // apiKeys: { openaiKey: '...', geminiKey: '...' } // Potentially send keys (more on this later)
        }),
      });

      // Check if the request was successful
      if (!response.ok) {
         // Handle errors (e.g., show error message, redirect if unauthorized)
         const errorData = await response.json();
         throw new Error(errorData.error || `API Error: ${response.status}`);
      }

      const data = await response.json(); // Parse the JSON response
      console.log('AI generated form data received:', data);

      // The AI's response should be a form structure
      if (data.fields && Array.isArray(data.fields)) {
         setGeneratedForm(data); // Update state to show the generated form
      } else {
         throw new Error('AI response had unexpected format.');
      }

    } catch (err: any) {
      console.error('Error generating form:', err);
      setError(err.message); // Show error to user
    } finally {
      setLoading(false); // Hide loading state
    }
  };

  // ... rest of the component (rendering the input, button, and the generated form preview)

  return (
    <div>
      {/* Input field for the prompt */}
      <textarea
        value={prompt}
        onChange={(e) => setPrompt(e.target.value)}
        placeholder={t('Describe the form or survey you need...')}
        rows={6}
        // ... styling ...
      />
      {/* Button to trigger generation */}
      <button onClick={handleGenerateContent} disabled={loading}>
        {loading ? 'Creating...' : 'Generate Form/Survey'}
      </button>

      {/* Display generated form preview here */}
      {generatedForm && (
        <div>
          <h2>Generated Form Preview</h2>
          {/* ... render the form fields from generatedForm.fields ... */}
        </div>
      )}

      {/* Display error message here */}
      {error && (
        <div className="text-red-500">{error}</div>
      )}
    </div>
  );
}
```

This client component is responsible for getting the user's input and sending it to the backend using a `fetch` request to `/api/generate-form`.

## Under the Hood: Generating from Text

When the `/api/generate-form/route.ts` route receives the prompt, it acts as an intermediary between the client and the actual AI service (like OpenAI or Google Gemini).

Here's a sequence diagram showing the process:

```mermaid
sequenceDiagram
    participant UserBrowser as User (Browser)
    participant GeneratePage as GeneratePage (Client Component)
    participant GenerateAPIRoute as Backend API (/api/generate-form)
    participant AIService as AI Service (OpenAI/Gemini)

    UserBrowser->>GeneratePage: Enters description & clicks Generate
    GeneratePage->>GenerateAPIRoute: POST request with prompt & type
    GenerateAPIRoute->>GenerateAPIRoute: Reads API Keys (from env/config)
    GenerateAPIRoute->>AIService: Sends Prompt + System Instructions
    AIService-->>GenerateAPIRoute: Returns JSON Form Structure
    GenerateAPIRoute->>GenerateAPIRoute: Parses & Validates JSON
    GenerateAPIRoute-->>GeneratePage: Sends Validated JSON Form Structure
    GeneratePage->>UserBrowser: Displays Form Preview
```

Let's look at simplified snippets from the backend API route (`app\api\generate-form\route.ts`):

```typescript
// Inside app\api\generate-form\route.ts (Simplified POST)
import { NextResponse } from 'next/server';
import OpenAI from 'openai'; // Library for OpenAI
import { GoogleGenerativeAI } from '@google/generative-ai'; // Library for Gemini
// Import centralized API key config (More on this later)
import { AI_API_KEYS } from '../../../config/apiKeys';

// Function to get the system prompt based on language/type (Turkish support shown in context)
function getSystemPrompts(language: string, type: 'form' | 'survey'): string {
  // This function returns a detailed text prompt instructing the AI
  // to generate a form/survey structure in JSON format.
  // It's simplified here but includes instructions on required fields, JSON structure, etc.
  // ... (implementation details hidden) ...
  return `Generate a JSON structure for a ${type} based on the user's description.
          Return ONLY JSON like { "name": "...", "description": "...", "fields": [...] }.`;
}

// Function to call OpenAI API
async function generateWithOpenAI(prompt: string, model: string, type: 'form' | 'survey', language: string = 'en'): Promise<string | null> {
  // Get API key from config or env vars
  const openaiApiKey = AI_API_KEYS.OPENAI;
  if (!openaiApiKey || openaiApiKey === 'BROWSER_ENVIRONMENT') {
     // In a real app, handle missing key appropriately (e.g., error response)
     console.error('OpenAI API key is missing or invalid');
     throw new Error('OpenAI API key is not configured.');
  }
  
  const openai = new OpenAI({ apiKey: openaiApiKey }); // Initialize OpenAI client
  const systemPrompt = getSystemPrompts(language, type); // Get instructions for the AI

  try {
    // Call the OpenAI chat completion endpoint
    const response = await openai.chat.completions.create({
      model: model, // e.g., 'gpt-3.5-turbo'
      messages: [
        { role: 'system', content: systemPrompt }, // Our instructions
        { role: 'user', content: prompt }, // User's description
      ],
      temperature: 0.7, // Controls randomness of output
      // ... other parameters ...
    });
    
    // Return the AI's text response (which should be JSON)
    return response.choices[0]?.message?.content || null;
  } catch (error: any) {
     console.error("Error calling OpenAI:", error);
     throw error; // Re-throw to be handled by the main POST function
  }
}
```

This snippet shows the `generateWithOpenAI` function. It gets the necessary API key (we'll touch on key management later), constructs messages for the AI (including a system prompt explaining *how* to format the output as JSON and the user's actual request), and then calls the OpenAI API.

Similarly, there's a function for Google Gemini:

```typescript
// Inside app\api\generate-form\route.ts (Simplified generateWithGemini)
import { GoogleGenerativeAI } from '@google/generative-ai';

async function generateWithGemini(prompt: string, model: string, type: 'form' | 'survey', language: string = 'en'): Promise<string | null> {
  const geminiApiKey = AI_API_KEYS.GEMINI;
   if (!geminiApiKey || geminiApiKey === 'BROWSER_ENVIRONMENT') {
     console.error('Gemini API key is missing or invalid');
     throw new Error('Gemini API key is not configured.');
  }
  
  const genAI = new GoogleGenerativeAI(geminiApiKey); // Initialize Gemini client
  const systemPrompt = getSystemPrompts(language, type); // Get instructions for the AI

  try {
     // Get the specific model
     const genModel = genAI.getGenerativeModel({ model: model }); // e.g., 'gemini-1.5-pro'
     
     // Call the Gemini content generation endpoint
     const result = await genModel.generateContent([systemPrompt, prompt]);
     
     // Return the AI's text response (which should be JSON)
     return result.response.text();
  } catch (error: any) {
     console.error("Error calling Gemini:", error);
     throw error; // Re-throw
  }
}
```

The main POST function in `/api/generate-form/route.ts` orchestrates this:

```typescript
// Inside app\api\generate-form\route.ts (Simplified POST body)
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { prompt, provider, model, type = 'form' } = body;

    if (!prompt) {
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 });
    }

    // Optional: Detect language of the prompt to use appropriate system prompts
    // let language = await detectLanguage(prompt, provider, model, apiKeys); // See code context

    let jsonResponse: string | null = null;

    // Call the appropriate AI generation function
    if (provider === 'openai') {
      jsonResponse = await generateWithOpenAI(prompt, model, type, /* language */);
    } else if (provider === 'gemini') {
      jsonResponse = await generateWithGemini(prompt, model, type, /* language */);
    } else {
      return NextResponse.json({ error: "Invalid provider" }, { status: 400 });
    }

    if (!jsonResponse) {
      return NextResponse.json({ error: "Failed to get response from AI" }, { status: 500 });
    }

    // Parse and validate the generated JSON
    let formData = parseAndValidateForm(jsonResponse); // See code context

    // Return the structured form data to the client
    return NextResponse.json(formData);

  } catch (error: any) {
    console.error("Error in generate-form API:", error);
    // Handle specific errors (e.g., API key issues, rate limits)
    // ... (error handling logic shown in context) ...
    return NextResponse.json({ error: error.message || "An unexpected error occurred" }, { status: 500 });
  }
}

// Function to parse and validate the generated form JSON (simplified)
function parseAndValidateForm(jsonString: string): any {
  try {
     // The AI might sometimes include extra text or markdown around the JSON.
     // This function tries to clean that up and then parse the JSON.
     let cleanedJsonString = jsonString.trim();
     // Example cleanup: remove ```json ... ``` markdown
     const jsonMatch = cleanedJsonString.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
     if (jsonMatch && jsonMatch[1]) {
         cleanedJsonString = jsonMatch[1].trim();
     }
     // Example cleanup: find the first { and last }
     const jsonStartRegex = /\{[\s\S]*\}/;
     const fullJsonMatch = cleanedJsonString.match(jsonStartRegex);
     if (fullJsonMatch) {
        cleanedJsonString = fullJsonMatch[0];
     }

     const formData = JSON.parse(cleanedJsonString);

     // Basic validation: check if required properties exist
     if (!formData.name || !Array.isArray(formData.fields)) {
         throw new Error("Invalid structure: missing name or fields array");
     }
     // Validate each field... (see full code for details)

     return formData;

  } catch (error: any) {
     console.error("Parsing/Validation failed:", error);
     throw new Error(`Failed to parse or validate AI response: ${error.message}`);
  }
}
```

This `POST` function receives the request, calls the chosen AI provider function (`generateWithOpenAI` or `generateWithGemini`), and then importantly, calls `parseAndValidateForm`. This validation step is crucial because AI models don't always return perfectly formatted JSON or might hallucinate incorrect field types. The `parseAndValidateForm` function tries to make the AI's output more robust and ensures it fits the expected structure before sending it back to the client.

## Use Case 2: Analyzing a Document for Form Fields

Another powerful aspect of AI Integration is analyzing existing documents. Let's look at that use case: **A user wants to create a form by uploading a PDF or image of a paper form.**

The user navigates to a dedicated page for document analysis (`app\document-analysis\page.tsx`), uploads a file, and clicks "Analyze".

Here's what happens:

1.  The user selects a file (PDF, JPG, PNG, etc.) using a file input on the client-side page (`app\document-analysis\page.tsx`).
2.  They click the "Analyze" button.
3.  The client-side code reads the file content (often converting images to base64 format) and sends it along with the chosen AI provider/model to a backend API route (`/api/analyze-document`).

Here's a simplified look at the client component (`app\document-analysis\page.tsx`) handling the file upload and analysis request:

```typescript
// Inside app\document-analysis\page.tsx (Simplified)
'use client';

import { useState } from 'react';
// ... other imports

export default function DocumentAnalysis() {
  // State to hold the selected file
  const [file, setFile] = useState<File | null>(null);
  // State for loading indicator
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  // State to hold the AI's generated form structure
  const [formPreview, setFormPreview] = useState<any>(null);
  // State for errors
  const [error, setError] = useState<string | null>(null);
  // State for selected AI provider/model (gemini, openai, etc.)
  const [selectedModel, setSelectedModel] = useState({ provider: 'gemini', name: 'gemini-1.5-pro' });

  // Handle file selection (e.g., from drag/drop or file input)
  const handleFileChange = (selectedFile: File | null) => {
     if (selectedFile) {
        // Basic file type check
        if (selectedFile.type === 'application/pdf' || selectedFile.type.startsWith('image/')) {
          setFile(selectedFile);
          setError(null); // Clear previous error
        } else {
          setError('Unsupported file format. Please upload a PDF or image.');
          setFile(null); // Clear file
        }
     } else {
        setFile(null);
        setError(null);
     }
  };

  // Function called when the Analyze button is clicked
  const analyzeDocument = async () => {
    if (!file) return; // Don't analyze if no file selected

    setIsAnalyzing(true); // Show loading state
    setError(null); // Clear previous errors
    setFormPreview(null); // Clear previous result

    const formData = new FormData(); // Use FormData to send file
    formData.append('file', file);
    formData.append('provider', selectedModel.provider);
    formData.append('model', selectedModel.name);
    // Optionally append API keys if needed (see API Key Management section)
    // formData.append('openaiApiKey', localStorage.getItem('openai_api_key') || '');


    try {
      console.log('Sending document analysis request to AI API...');
      // Make the POST request to our backend API route
      const response = await fetch('/api/analyze-document', {
        method: 'POST',
        body: formData, // Send the file and other data
        // credentials: 'include', // Might be needed for authentication check
      });

      // Check if the request was successful
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API Error: ${response.status}`);
      }

      const data = await response.json(); // Parse the JSON response
      console.log('AI analyzed document, received form data:', data);

      // The AI's response should be a form structure
      if (data.formFields && Array.isArray(data.formFields)) {
        setFormPreview(data); // Update state to show the generated form
      } else {
        throw new Error('AI response had unexpected format.');
      }

    } catch (err: any) {
      console.error('Error analyzing document:', err);
      setError(err.message); // Show error to user
    } finally {
      setIsAnalyzing(false); // Hide loading state
    }
  };

  // ... rest of the component (rendering file input, button, and preview)

  return (
     <div>
       {/* File upload area */}
       <input type="file" onChange={(e) => handleFileChange(e.target.files?.[0] || null)} accept=".pdf,image/*" />
       {/* Button to trigger analysis */}
       <button onClick={analyzeDocument} disabled={!file || isAnalyzing}>
         {isAnalyzing ? 'Analyzing...' : 'Analyze Document'}
       </button>

       {/* Display generated form preview here */}
       {formPreview && (
         <div>
           <h2>Analyzed Form Preview</h2>
           {/* ... render the form fields from formPreview.formFields ... */}
         </div>
       )}

       {/* Display error message here */}
       {error && (
         <div className="text-red-500">{error}</div>
       )}
     </div>
  );
}
```

This component uses standard HTML file input or drag-and-drop (`handleDragOver`, `handleDragLeave`, `handleDrop` - shown in the full code context) to get the user's file and then sends it to the backend via a `FormData` POST request to `/api/analyze-document`.

## Under the Hood: Analyzing a Document

The `/api/analyze-document/route.ts` route is similar to `/api/generate-form`, but it needs to handle file data and use AI models capable of "seeing" or processing images/documents (often called Vision models).

Here's a sequence diagram for document analysis:

```mermaid
sequenceDiagram
    participant UserBrowser as User (Browser)
    participant DocumentAnalysisPage as DocumentAnalysis (Client Component)
    participant AnalyzeAPIRoute as Backend API (/api/analyze-document)
    participant AIServiceVision as AI Vision Service (OpenAI/Gemini)

    UserBrowser->>DocumentAnalysisPage: Uploads Document & clicks Analyze
    DocumentAnalysisPage->>AnalyzeAPIRoute: POST request with File Data
    AnalyzeAPIRoute->>AnalyzeAPIRoute: Converts File to Base64 Image
    AnalyzeAPIRoute->>AIServiceVision: Sends Base64 Image + Prompt
    AIServiceVision-->>AnalyzeAPIRoute: Returns JSON Form Structure
    AnalyzeAPIRoute->>AnalyzeAPIRoute: Parses & Validates JSON, Transforms
    AnalyzeAPIRoute-->>DocumentAnalysisPage: Sends Validated JSON Form Structure
    DocumentAnalysisPage->>UserBrowser: Displays Form Preview
```

Let's look at simplified snippets from the backend API route (`app\api\analyze-document\route.ts`):

```typescript
// Inside app\api\analyze-document\route.ts (Simplified POST)
import { NextResponse } from 'next/server';
import OpenAI from 'openai'; // For OpenAI Vision
import { GoogleGenerativeAI } from '@google/generative-ai'; // For Gemini Vision/Processing
// Import API key service (More on this later)
import apiKeyService from '@/services/apiKeyService';

const FORM_ANALYSIS_PROMPT = `Analyze this document (image or PDF page) and create a form structure.
                                Return ONLY a JSON object like { "title": "...", "fields": [...] }.`; // Simplified

// Function to call OpenAI Vision API
async function analyzeWithOpenAI(base64Image: string, model: string, apiKey: string) {
  try {
    const openai = new OpenAI({ apiKey: apiKey });
    const response = await openai.chat.completions.create({
      model: model, // e.g., 'gpt-4-vision-preview'
      messages: [{
        role: "user" as const,
        content: [
          { type: "text", text: FORM_ANALYSIS_PROMPT },
          { type: "image_url", image_url: { url: `data:image/jpeg;base64,${base64Image}` } }
        ]
      }],
      max_tokens: 4000,
      response_format: { type: "json_object" }
    });
    return response.choices[0]?.message?.content; // AI response (should be JSON string)
  } catch (error) {
    console.error('OpenAI Vision Error:', error);
    throw error;
  }
}

// Function to call Gemini Vision API
async function analyzeWithGemini(base64Image: string, model: string, apiKey: string) {
  try {
    const genAI = new GoogleGenerativeAI(apiKey);
    const geminiModel = genAI.getGenerativeModel({ model: model }); // e.g., 'gemini-1.5-pro-latest'

    // Send the image data with the prompt
    const result = await geminiModel.generateContent([
      FORM_ANALYSIS_PROMPT,
      { inlineData: { mimeType: "image/jpeg", data: base64Image } }
    ]);

    const response = await result.response;
    return response.text(); // AI response (should be JSON string)

  } catch (error) {
    console.error('Gemini Vision Error:', error);
    throw error;
  }
}

export async function POST(req: Request) {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const provider = formData.get('provider') as string || 'openai';
    const model = formData.get('model') as string || 'gpt-4-vision'; // Default vision model

    // Get API keys (potentially from client, but preferably server config)
    // The apiKeyService helps manage this (more below)
    const openaiApiKey = apiKeyService.getApiKey('openai');
    const geminiApiKey = apiKeyService.getApiKey('gemini');

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    // Convert file to base64 format (necessary for sending to Vision APIs)
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const base64Image = buffer.toString('base64');

    let response;
    // Call the appropriate AI analysis function
    if (provider === 'openai' && openaiApiKey) {
      response = await analyzeWithOpenAI(base64Image, model, openaiApiKey);
    } else if (provider === 'gemini' && geminiApiKey) {
      response = await analyzeWithGemini(base64Image, model, geminiApiKey);
    } else if (!openaiApiKey && !geminiApiKey) {
        return NextResponse.json(
            { error: 'API keys for AI providers are not configured.', needApiKey: true },
            { status: 401 } // Indicate authentication/configuration issue
        );
    }
    else {
        return NextResponse.json({ error: 'Selected provider not available (API key missing?)' }, { status: 400 });
    }


    if (!response) {
      return NextResponse.json({ error: "Failed to get analysis from AI" }, { status: 500 });
    }

    // Parse and validate the generated JSON (similar to text generation)
    let parsedResponse = JSON.parse(response); // Basic parse first

    // Transform the AI's output into our Form model structure (Chapter 2)
    const formStructureForSaving = {
      name: parsedResponse.title || 'Analyzed Document Form',
      description: 'Generated from document analysis',
      type: 'form', // Always 'form' for document analysis? Or depends on content?
      formFields: parsedResponse.fields.map((field: any) => ({
        // Map AI fields to our FormField structure
        name: field.id || field.name || field.label.toLowerCase().replace(/\s+/g, '_'), // Ensure a valid name
        label: field.label,
        type: field.type, // Ensure type is one of our supported types ('text', 'number', 'select', etc.)
        required: field.required || false,
        options: Array.isArray(field.options) ? field.options.map((opt: any) => ({
           label: typeof opt === 'string' ? opt : opt.label,
           value: typeof opt === 'string' ? opt : (opt.value || opt.label),
        })) : [],
        // ... add other properties as needed by Form model (placeholder, description etc.)
      })),
      // Store the original AI output for debugging if needed
      generatedJson: parsedResponse,
       // Note: Ownership (userId/userEmail) is added when saving the form later (Chapter 2)
    };

    // Return the structured form data to the client
    return NextResponse.json(formStructureForSaving);

  } catch (error: any) {
    console.error('Error processing document analysis request:', error);
    // Handle API key errors, file size errors, AI errors etc.
    // ... (error handling logic shown in context) ...
     if (error.message.includes('API key') || error.message.includes('authentication') || error.message.includes('configured')) {
         return NextResponse.json({ error: error.message, needApiKey: true }, { status: 401 });
     }
    return NextResponse.json({ error: error.message || 'Error analyzing document' }, { status: 500 });
  }
}
```

This API route performs the heavy lifting for document analysis. It receives the file, prepares it for the AI (converting to base64 is common for image/vision models), calls the appropriate AI service (`analyzeWithOpenAI` or `analyzeWithGemini`), and then takes the AI's response (a JSON structure representing fields found in the document) and transforms it into the `formFields` format that Myform understands for its [Form Structure & Management](02_form_structure___management_.md).

## AI API Key Management

You might have noticed that the backend API routes need API keys (`OPENAI_API_KEY`, `GEMINI_API_KEY`) to communicate with the AI services. These keys are sensitive and should **never** be hardcoded directly in client-side code or exposed in environment variables accessible client-side.

In Myform, API keys are managed securely on the **server side**, typically through:

1.  **Environment Variables:** Setting `process.env.OPENAI_API_KEY` and `process.env.GEMINI_API_KEY` on the server where the application is deployed.
2.  **Configuration Files:** Optionally, Myform uses files like `config\production-keys.js` (checked into source control, used for static keys) and `server-config.js` (NOT checked into source control, used for sensitive keys on specific deployments). The code prioritizes keys found in `server-config.js`, then `production-keys.js`, then environment variables.
3.  **`apiKeyService.ts`:** A helper service (`services\apiKeyService.ts`) is used on the server to access these keys based on the defined priority.
4.  **Client-Side Handling (Development Only):** In a development environment (`NODE_ENV !== 'production'`), Myform *might* prompt the user to enter their own API keys via a modal (`components\ApiKeyModal.tsx`) if server-side keys are not configured. These keys are often stored temporarily (e.g., in browser `localStorage`) and sent with the API requests, but this is primarily a convenience for development and is **disabled in production**.

The API routes (`/api/generate-form` and `/api/analyze-document`) fetch the keys using `apiKeyService` *before* making calls to the external AI services. If keys are missing, they return an error (sometimes indicating `needApiKey: true` to the client in development).

## From AI Output to Saved Form

After the AI successfully generates a form structure (either from text or document analysis), the resulting JSON data is sent back to the client-side component (`app\features\ai-form-builder\generate\page.tsx` or `app\document-analysis\page.tsx`).

This client component then displays this structure as an editable *preview*. The user can review the suggested fields, change labels, types, or options, add new fields, or remove unnecessary ones, essentially fine-tuning the AI's suggestion.

Once the user is happy with the form structure in the preview, they can click a "Save Form" button. This button triggers a POST request to the `/api/forms` API route, sending the final, edited form structure to be saved in the database.

```typescript
// Inside app\features\ai-form-builder\generate\page.tsx (Simplified Save Form handler)

  const handleSaveForm = async () => {
    // Ensure we have a generated form preview to save
    if (!generatedForm) {
      alert(t('Please generate a form first.'));
      return;
    }

    // Ensure user is authenticated (Chapter 1 check)
    // ... (authentication check logic, potentially redirecting to login) ...

    setIsSaving(true); // Show saving state
    setSaveError(null); // Clear errors

    try {
      // Prepare the form data to match our Form model structure (Chapter 2)
      const formToSave = {
        name: generatedForm.name || 'Untitled Form',
        description: generatedForm.description || '',
        type: generationType, // 'form' or 'survey'
        // Map the AI's generated fields (potentially edited by the user)
        formFields: generatedForm.fields.map((field: any) => ({
          // Ensure fields have the correct structure expected by our Form model
          name: field.name,
          label: field.label,
          type: field.type,
          required: field.required || false,
          placeholder: field.placeholder || '',
          options: field.options || [],
          // ... add other properties like validation, description, etc.
        })),
        // Store the original AI output
        generatedJson: generatedForm,
        // userId and userEmail are added by the API route based on the logged-in user (Chapter 2)
      };

      console.log('Sending final form structure to /api/forms for saving...');
      // Send the form data to the standard form creation API route
      const response = await fetch('/api/forms', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include', // Ensure cookies (auth) are sent
        body: JSON.stringify(formToSave),
      });

      if (!response.ok) {
         // Handle errors (e.g., validation failed, unauthorized, server error)
         const errorData = await response.json();
         throw new Error(errorData.error || `Save API Error: ${response.status}`);
      }

      const result = await response.json();
      console.log('Form saved successfully:', result.formId);

      // Show success message or modal and redirect to the forms list
      // setShowSuccessModal(true);
      // setTimeout(() => router.push('/forms'), 2000);

    } catch (err: any) {
      console.error('Error saving form:', err);
      setSaveError(err.message);
    } finally {
      setIsSaving(false);
    }
  };
```

This flow connects the AI generation process seamlessly with the existing [Form Structure & Management](02_form_structure___management_.md) system. The AI acts as a powerful *helper* to build the initial structure, which is then saved using the same mechanisms as manually created forms.

## Conclusion

In this chapter, we explored the exciting concept of **AI Integration** in Myform. We learned how AI models serve as intelligent assistants to drastically speed up the form creation process. We saw how Myform uses backend API routes to communicate with external AI services like OpenAI and Google Gemini, either by sending them text descriptions or document images for analysis. We also understood how the AI's output is then transformed into Myform's internal [Form Structure & Management](02_form_structure___management_.md) format and how API keys are handled securely on the server side.

This AI integration empowers users to go from an idea or a document to a functional form structure much faster, which they can then fine-tune and manage using the features we discussed in previous chapters.

Now that we've seen how client components interact with our backend API routes for AI generation and form management, let's take a closer look at how these crucial API routes are built using Next.js.

[Chapter 5: Next.js API Routes](05_next_js_api_routes_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)